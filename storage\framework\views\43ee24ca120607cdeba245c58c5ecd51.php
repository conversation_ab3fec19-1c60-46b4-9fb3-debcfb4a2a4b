<?php $__env->startSection('content'); ?>
<main class="main-content">
    <section class="page-header-area pt-10 pb-9" data-bg-color="#FFF3DA">
        <div class="container">
            <div class="row">
                <div class="col-md-5">
                    <div class="page-header-st3-content text-center text-md-start">
                        <ol class="breadcrumb justify-content-center justify-content-md-start">
                            <li class="breadcrumb-item"><a class="text-dark" href="<?php echo e(route('home')); ?>">Home</a>
                            </li>
                            <li class="breadcrumb-item"><a class="text-dark" href="<?php echo e(route('shop')); ?>">Products</a>
                            </li>
                            <li class="breadcrumb-item active text-dark"
                                aria-current="page"><?php echo e($product->name); ?></li>
                        </ol>
                        <h2 class="page-header-title"><?php echo e($product->name); ?></h2>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="section-space">
        <div class="container">
            <div class="row product-details">
                <div class="col-lg-6">
                    <div class="product-details-thumb">
                        <img id="product-main-image"
                            src="<?php echo e($product->image ? asset('storage/' . $product->image) : asset('assets/storefront/images/shop/product-details/1.webp')); ?>"
                            width="570" height="693" alt="<?php echo e($product->name); ?>">
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="product-details-content">
                        <h5 class="product-details-collection"><?php echo e($product->category->name ?? 'Collection'); ?></h5>
                        <h3 class="product-details-title"><?php echo e($product->name); ?></h3>

                        <div class="product-details-review">
                            <div class="product-review-icon">
                                <i class="fa fa-star-o"></i>
                                <i class="fa fa-star-o"></i>
                                <i class="fa fa-star-o"></i>
                                <i class="fa fa-star-o"></i>
                                <i class="fa fa-star-half-o"></i>
                            </div>
                            <button type="button" class="product-review-show">150 reviews</button>
                        </div>
                        <div id="variant-options-container" class="mb-4"></div>

                        <div class="product-details-pro-qty">
                            <div class="pro-qty">
                                <input type="number" id="quantity" title="Quantity" value="1" min="1">
                            </div>
                        </div>

                        <div class="product-details-action">
                            <h6 class="price" id="product-price">Select options to see price</h6>
                            <div class="product-details-cart-wishlist">
                                <button type="button" class="btn-wishlist" data-bs-toggle="modal"
                                    data-bs-target="#action-WishlistModal"><i class="fa fa-heart-o"></i>
                                </button>
                                <button type="button" class="btn action-btn-cart" id="add-to-cart-btn" disabled>Add
                                    to cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="nav product-details-nav" id="product-details-nav-tab" role="tablist">
                        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#description-tab"
                            type="button">Description
                        </button>
                        
                    </div>
                    <div class="tab-content" id="product-details-nav-tabContent">
                        <div class="tab-pane fade show active" id="description-tab" role="tabpanel">
                            <?php echo nl2br(e($product->description)); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php if($relatedProducts->isNotEmpty()): ?>
    <section class="section-space">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-title">
                        <h2 class="title">Related Products</h2>
                        <p class="m-0">Check out other products in the same category.</p>
                    </div>
                </div>
            </div>
            <div class="row mb-n10">
                <div class="col-12">
                    <div class="swiper related-product-slide-container">
                        <div class="swiper-wrapper">
                            <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="swiper-slide mb-10">
                                
                                <?php if (isset($component)) { $__componentOriginal3fd2897c1d6a149cdb97b41db9ff827a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3fd2897c1d6a149cdb97b41db9ff827a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.product-card','data' => ['product' => $related]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('product-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['product' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($related)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3fd2897c1d6a149cdb97b41db9ff827a)): ?>
<?php $attributes = $__attributesOriginal3fd2897c1d6a149cdb97b41db9ff827a; ?>
<?php unset($__attributesOriginal3fd2897c1d6a149cdb97b41db9ff827a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3fd2897c1d6a149cdb97b41db9ff827a)): ?>
<?php $component = $__componentOriginal3fd2897c1d6a149cdb97b41db9ff827a; ?>
<?php unset($__componentOriginal3fd2897c1d6a149cdb97b41db9ff827a); ?>
<?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>
</main>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const productData = <?php echo json_encode($product, 15, 512) ?>;
        const variants = productData.variants;
        const optionsContainer = document.getElementById('variant-options-container');
        const priceEl = document.getElementById('product-price');
        const addToCartBtn = document.getElementById('add-to-cart-btn');

        // 1. Group attributes and values from all variants
        const attributes = {};
        variants.forEach(variant => {
            variant.attribute_values.forEach(attrValue => {
                const attrName = attrValue.attribute.name;
                if (!attributes[attrName]) {
                    attributes[attrName] = {
                        id: attrValue.attribute.id,
                        values: {}
                    };
                }
                attributes[attrName].values[attrValue.id] = attrValue.value;
            });
        });

        // 2. Render attribute selection UI
        let optionsHtml = '';
        for (const attrName in attributes) {
            optionsHtml += `<div class="product-details-qty-list mb-4"><h5 class="title">${attrName}</h5>`;
            for (const valueId in attributes[attrName].values) {
                optionsHtml += `
                <div class="qty-list-check">
                    <input class="form-check-input variant-option" type="radio" name="attribute_${attributes[attrName].id}" id="option_${valueId}" value="${valueId}">
                    <label class="form-check-label" for="option_${valueId}">${attributes[attrName].values[valueId]}</label>
                </div>`;
            }
            optionsHtml += `</div>`;
        }
        optionsContainer.innerHTML = optionsHtml;

        // 3. Add event listeners and update UI based on selection
        const optionRadios = optionsContainer.querySelectorAll('.variant-option');
        optionRadios.forEach(radio => radio.addEventListener('change', updateVariantDetails));

        function updateVariantDetails() {
            const selectedOptions = Array.from(optionsContainer.querySelectorAll('.variant-option:checked'));

            // Check if all attribute groups have a selection
            if (selectedOptions.length !== Object.keys(attributes).length) {
                priceEl.textContent = 'Please select all options';
                addToCartBtn.disabled = true;
                return;
            }

            const selectedValueIds = selectedOptions.map(input => input.value).sort();

            const matchedVariant = variants.find(variant => {
                const variantValueIds = variant.attribute_values.map(v => v.id.toString()).sort();
                return selectedValueIds.length === variantValueIds.length &&
                    selectedValueIds.every((id, index) => id === variantValueIds[index]);
            });

            if (matchedVariant) {
                let priceHtml = '';
                const price = parseFloat(matchedVariant.price);
                const discountPrice = matchedVariant.discount_price ?
                    parseFloat(matchedVariant.discount_price) :
                    null;

                if (discountPrice && discountPrice < price) {
                    priceHtml = `<p class="price">${discountPrice.toLocaleString(
                                'vi-VN')}Đ</p> <p class="price-old">${price.toLocaleString(
                                'vi-VN')}Đ</p>`;
                } else {
                    priceHtml = `<span class="price">${price.toLocaleString('vi-VN')}Đ</span>`;
                }
                priceEl.innerHTML = priceHtml;
                addToCartBtn.dataset.variantId = matchedVariant.id;
                addToCartBtn.disabled = false;
            } else {
                priceEl.textContent = 'This combination is not available';
                addToCartBtn.dataset.variantId = '';
                addToCartBtn.disabled = true;
            }
        }

        // Handle Simple Product case (no options to select)
        if (Object.keys(attributes).length === 0 && variants.length === 1) {
            const simpleVariant = variants[0];
            let priceHtml = '';
            const price = parseFloat(simpleVariant.price);
            const discountPrice = simpleVariant.discount_price ?
                parseFloat(simpleVariant.discount_price) :
                null;

            if (discountPrice && discountPrice < price) {
                priceHtml = `<p class="price">${discountPrice.toLocaleString(
                            'vi-VN')}Đ</p> <p class="price-old">${price.toLocaleString('vi-VN')}Đ</p>`;
            } else {
                priceHtml = `<p class="price">${price.toLocaleString('vi-VN')}Đ</p>`;
            }
            priceEl.innerHTML = priceHtml;
            addToCartBtn.dataset.variantId = variants[0].id;
            addToCartBtn.disabled = false;
        }

        // Add event listener for add to cart button
        addToCartBtn.addEventListener('click', function() {
            const variantId = this.dataset.variantId;
            const quantity = document.getElementById('quantity').value || 1;

            if (variantId && window.addToCart) {
                window.addToCart(variantId, quantity, this);
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('storefront.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\projects\mint_cosmetics\resources\views/storefront/product-detail.blade.php ENDPATH**/ ?>