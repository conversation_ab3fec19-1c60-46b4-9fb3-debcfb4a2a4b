{"__meta": {"id": "01K409CR9ZZX7V6C04KGCVZGH7", "datetime": "2025-08-31 14:44:39", "utime": **********.359529, "method": "GET", "uri": "/products/9/quick-view", "ip": "::1"}, "messages": {"count": 1, "messages": [{"message": "[14:44:39] LOG.info: Quick view product found: {\n    \"id\": 9,\n    \"name\": \"Free Shipping1212\",\n    \"variants_count\": 2\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.350482, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": **********.292418, "end": **********.359548, "duration": 0.06713008880615234, "duration_str": "67.13ms", "measures": [{"label": "Booting", "start": **********.292418, "relative_start": 0, "end": **********.313385, "relative_end": **********.313385, "duration": 0.*****************, "duration_str": "20.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.313394, "relative_start": 0.*****************, "end": **********.359551, "relative_end": 2.86102294921875e-06, "duration": 0.046156883239746094, "duration_str": "46.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.316256, "relative_start": 0.023838043212890625, "end": **********.316695, "relative_end": **********.316695, "duration": 0.00043892860412597656, "duration_str": "439μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.358795, "relative_start": 0.*****************, "end": **********.358976, "relative_end": **********.358976, "duration": 0.00018095970153808594, "duration_str": "181μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3757168, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014719999999999999, "accumulated_duration_str": "14.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.319185, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.319572, "duration": 0.01065, "duration_str": "10.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 72.351}, {"sql": "select * from `products` where `id` = '9' and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.332425, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 72.351, "width_percent": 3.125}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.3351958, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:46", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=46", "ajax": false, "filename": "ShopController.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 75.476, "width_percent": 5.299}, {"sql": "select `attribute_values`.*, `attribute_value_product_variant`.`product_variant_id` as `pivot_product_variant_id`, `attribute_value_product_variant`.`attribute_value_id` as `pivot_attribute_value_id` from `attribute_values` inner join `attribute_value_product_variant` on `attribute_values`.`id` = `attribute_value_product_variant`.`attribute_value_id` where `attribute_value_product_variant`.`product_variant_id` in (61, 62)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.338185, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:46", "source": {"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=46", "ajax": false, "filename": "ShopController.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 80.774, "width_percent": 4.552}, {"sql": "select * from `attributes` where `attributes`.`id` in (4, 5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.340961, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:46", "source": {"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=46", "ajax": false, "filename": "ShopController.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 85.326, "width_percent": 4.484}, {"sql": "select * from `categories` where `categories`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.344659, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:46", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=46", "ajax": false, "filename": "ShopController.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 89.81, "width_percent": 5.91}, {"sql": "select * from `brands` where `brands`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.3470461, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:46", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=46", "ajax": false, "filename": "ShopController.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 95.72, "width_percent": 4.28}]}, "models": {"data": {"App\\Models\\AttributeValue": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttributeValue.php&line=1", "ajax": false, "filename": "AttributeValue.php", "line": "?"}}, "App\\Models\\ProductVariant": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}, "App\\Models\\Attribute": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 11, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 11}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/9/quick-view", "action_name": "products.quickView", "controller_action": "App\\Http\\Controllers\\Storefront\\ShopController@quickView", "uri": "GET products/{product}/quick-view", "controller": "App\\Http\\Controllers\\Storefront\\ShopController@quickView<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/ShopController.php:42-67</a>", "middleware": "web", "duration": "67.93ms", "peak_memory": "4MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-227588320 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-227588320\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-644284092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-644284092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1095954424 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mint-cosmetics.local/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imtza2xxYlppYkFkRGI5U2FhREpqU1E9PSIsInZhbHVlIjoidVJTYmluWkxIYmROb0luclR5Y1d3M0tTdlphbVg0NWlCWUZtbUora0tzVjJPNkFGL21WblQ1WEFmVnJJNWdwaXNyQ0o4TldKbmNIbS9pYWI3UzV2SXQxK044L3pzQjJzVzU0Skc2czViSGcwV1dUYzVLOTBFbzRWbzYrYklYNHEiLCJtYWMiOiJmMDk0NzJkNDk1MmExYTczMzZhYWU3YzEyMjhhM2RkMWE3ZmFlMjYwZGE1NjM2MThhYzQ3ODM1Y2E5Y2Q1ODYxIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IjYwNVJFbkpZTUt1d2tSY1NIaUZJS0E9PSIsInZhbHVlIjoiVGFBa0Q1K0JHTGFXSUFUUCsvcVR1ZlpkaG5MdmhBR0NqemNibGRBZ1JBYlJidTloaXE2alY1VWFNeVRKZzlpdzRTc1k0Y3BROU1VQTNIbDU5UnV0S3hNdVNPd00wMEtYdmdlL01XL2NxK3ZEWGxmM2IvcUhTTkxqaWNZUjZJKzEiLCJtYWMiOiJlMWZiYWNkMjAyYTYyMWJhMjY0ZThhNjQwYThjOTNhN2I2MjYxNTgwOWYyNzk2ZjZmNDhlOWY1NzhjNzA5OTJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095954424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-616271312 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616271312\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1620184662 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:44:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620184662\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1006494678 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://mint-cosmetics.local/products/free-shipping1212</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>16</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">12222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006494678\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/9/quick-view", "action_name": "products.quickView", "controller_action": "App\\Http\\Controllers\\Storefront\\ShopController@quickView"}, "badge": null}}