{"__meta": {"id": "01K40952V152EPAFDS4CQVHKZS", "datetime": "2025-08-31 14:40:28", "utime": **********.001569, "method": "GET", "uri": "/products/tra-pham1212", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 15, "start": **********.939562, "end": **********.001579, "duration": 0.062016963958740234, "duration_str": "62.02ms", "measures": [{"label": "Booting", "start": **********.939562, "relative_start": 0, "end": **********.959581, "relative_end": **********.959581, "duration": 0.020018815994262695, "duration_str": "20.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.959593, "relative_start": 0.020030975341796875, "end": **********.00158, "relative_end": 9.5367431640625e-07, "duration": 0.041986942291259766, "duration_str": "41.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.960862, "relative_start": 0.*****************, "end": **********.961051, "relative_end": **********.961051, "duration": 0.00018906593322753906, "duration_str": "189μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.989758, "relative_start": 0.050195932388305664, "end": **********.001415, "relative_end": **********.001415, "duration": 0.011656999588012695, "duration_str": "11.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: storefront.product-detail", "start": **********.990006, "relative_start": 0.*****************, "end": **********.990006, "relative_end": **********.990006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.99321, "relative_start": 0.*****************, "end": **********.99321, "relative_end": **********.99321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.app", "start": **********.995922, "relative_start": 0.05636000633239746, "end": **********.995922, "relative_end": **********.995922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.header", "start": **********.996677, "relative_start": 0.05711483955383301, "end": **********.996677, "relative_end": **********.996677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-search", "start": **********.997061, "relative_start": 0.057498931884765625, "end": **********.997061, "relative_end": **********.997061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-cart", "start": **********.997653, "relative_start": 0.058090925216674805, "end": **********.997653, "relative_end": **********.997653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-menu", "start": **********.997988, "relative_start": 0.0584259033203125, "end": **********.997988, "relative_end": **********.997988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-view-modal", "start": **********.998765, "relative_start": 0.05920290946960449, "end": **********.998765, "relative_end": **********.998765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-add-cart-modal", "start": **********.999649, "relative_start": 0.060086965560913086, "end": **********.999649, "relative_end": **********.999649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-wishlist-modal", "start": **********.000052, "relative_start": 0.06048989295959473, "end": **********.000052, "relative_end": **********.000052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.footer", "start": **********.000812, "relative_start": 0.06124997138977051, "end": **********.000812, "relative_end": **********.000812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 3961072, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "storefront.product-detail", "param_count": null, "params": [], "start": **********.989968, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/product-detail.blade.phpstorefront.product-detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fproduct-detail.blade.php&line=1", "ajax": false, "filename": "product-detail.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.993174, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "storefront.layouts.app", "param_count": null, "params": [], "start": **********.995888, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/app.blade.phpstorefront.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "storefront.partials.header", "param_count": null, "params": [], "start": **********.996645, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/header.blade.phpstorefront.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-search", "param_count": null, "params": [], "start": **********.997029, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-search.blade.phpstorefront.partials.aside-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-search.blade.php&line=1", "ajax": false, "filename": "aside-search.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-cart", "param_count": null, "params": [], "start": **********.997621, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-cart.blade.phpstorefront.partials.aside-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-cart.blade.php&line=1", "ajax": false, "filename": "aside-cart.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-menu", "param_count": null, "params": [], "start": **********.997946, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-menu.blade.phpstorefront.partials.aside-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-menu.blade.php&line=1", "ajax": false, "filename": "aside-menu.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-view-modal", "param_count": null, "params": [], "start": **********.998728, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-view-modal.blade.phpstorefront.layouts.quick-view-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-view-modal.blade.php&line=1", "ajax": false, "filename": "quick-view-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-add-cart-modal", "param_count": null, "params": [], "start": **********.999591, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-add-cart-modal.blade.phpstorefront.layouts.quick-add-cart-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-add-cart-modal.blade.php&line=1", "ajax": false, "filename": "quick-add-cart-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-wishlist-modal", "param_count": null, "params": [], "start": **********.000019, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-wishlist-modal.blade.phpstorefront.layouts.quick-wishlist-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-wishlist-modal.blade.php&line=1", "ajax": false, "filename": "quick-wishlist-modal.blade.php", "line": "?"}}, {"name": "storefront.partials.footer", "param_count": null, "params": [], "start": **********.00078, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/footer.blade.phpstorefront.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007019999999999999, "accumulated_duration_str": "7.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.962954, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.963169, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 36.325}, {"sql": "delete from `sessions` where `last_activity` <= 1756644027", "type": "query", "params": [], "bindings": [1756644027], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 280}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 177}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 118}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 63}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.967251, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:280", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 280}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=280", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "280"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 36.325, "width_percent": 4.843}, {"sql": "select * from `products` where `slug` = 'tra-pham1212' and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tra-pham1212"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.969537, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 41.168, "width_percent": 5.698}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.97218, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 46.866, "width_percent": 10.684}, {"sql": "select * from `brands` where `brands`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.974874, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 57.55, "width_percent": 6.41}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.976789, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 63.96, "width_percent": 4.701}, {"sql": "select `attribute_values`.*, `attribute_value_product_variant`.`product_variant_id` as `pivot_product_variant_id`, `attribute_value_product_variant`.`attribute_value_id` as `pivot_attribute_value_id` from `attribute_values` inner join `attribute_value_product_variant` on `attribute_values`.`id` = `attribute_value_product_variant`.`attribute_value_id` where `attribute_value_product_variant`.`product_variant_id` in (56)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.979571, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 68.661, "width_percent": 12.108}, {"sql": "select * from `attributes` where `attributes`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.982321, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 80.769, "width_percent": 5.128}, {"sql": "select * from `products` where `category_id` = 7 and `id` != 10 and `active` = 1 and `products`.`deleted_at` is null limit 10", "type": "query", "params": [], "bindings": [7, 10, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.983686, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=37", "ajax": false, "filename": "ProductController.php", "line": "37"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 85.897, "width_percent": 5.556}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.986075, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:37", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=37", "ajax": false, "filename": "ProductController.php", "line": "37"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 91.453, "width_percent": 8.547}]}, "models": {"data": {"App\\Models\\Product": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductVariant": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\AttributeValue": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttributeValue.php&line=1", "ajax": false, "filename": "AttributeValue.php", "line": "?"}}, "App\\Models\\Attribute": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}}, "count": 8, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 8}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/tra-pham1212", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\Storefront\\ProductController@show", "uri": "GET products/{product}", "controller": "App\\Http\\Controllers\\Storefront\\ProductController@show<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/ProductController.php:22-40</a>", "middleware": "web", "duration": "61.42ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-17862789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-17862789\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-26361152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-26361152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1274629408 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mint-cosmetics.local/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjhyU1BvNk5UKzVwK2c0cHZLbTlvYUE9PSIsInZhbHVlIjoiVmtqcGRIK1FxS09VT1J6SmFEdjRHWGd4VFhPc3ZORGVnUHBIYVlGTEVmVUMrdE9hSlRCYnBneE1vU0o3UFFHZnFHaVlnTDMrUXh0MW9iVHJtclJCUHl4NWY2cGVIUE41VFVTTDAraG45MUt2Yzdyd2QrV21rMGh6eUxheHhKcFQiLCJtYWMiOiIzOGJmMjE3OWNmYTI3NWYxZDY0NzVhNWQxYTc1MjJmOGNjMGJmM2ExYTFiYWNiNTMzNDQ1YjU1OGYwMGJmNmQ5IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IjdQcUxqTDQwODl1MWRFNHNGaThDZXc9PSIsInZhbHVlIjoiK2ZuWWVKQ3MwN1pPL20zVGEySjlhd2hPS1N2YTRETm9PN3UxZWNqRTF2OENaZWczRGlndEtkMzNRRFFMVlpYOW41ZzlrQUVpVHdETGNDUHBrRms3Y1J5NUpjbHlGWWZMN1RES2ZEbUcyVUdwdGE2WDMyMkdKMGZNVTJLQ1BENEQiLCJtYWMiOiI1ZTJiNmIyMjcyNTliOGRjMTg3MmI4YWUxMjcxMWQzODJiM2NmM2RhMDVmZTczMGVjN2RiODE5OTJkZWI2Y2QzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274629408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-297426144 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297426144\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1789791296 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:40:27 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789791296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-667186322 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://mint-cosmetics.local/products/9/quick-view</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>16</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">12222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667186322\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/tra-pham1212", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\Storefront\\ProductController@show"}, "badge": null}}