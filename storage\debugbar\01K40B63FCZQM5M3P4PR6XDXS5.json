{"__meta": {"id": "01K40B63FCZQM5M3P4PR6XDXS5", "datetime": "2025-08-31 15:15:58", "utime": **********.572678, "method": "GET", "uri": "/cart", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 14, "start": **********.528607, "end": **********.572692, "duration": 0.044085025787353516, "duration_str": "44.09ms", "measures": [{"label": "Booting", "start": **********.528607, "relative_start": 0, "end": **********.548789, "relative_end": **********.548789, "duration": 0.020182132720947266, "duration_str": "20.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.548798, "relative_start": 0.020191192626953125, "end": **********.572694, "relative_end": 2.1457672119140625e-06, "duration": 0.023895978927612305, "duration_str": "23.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.551635, "relative_start": 0.023028135299682617, "end": **********.552088, "relative_end": **********.552088, "duration": 0.00045299530029296875, "duration_str": "453μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.562145, "relative_start": 0.033538103103637695, "end": **********.572287, "relative_end": **********.572287, "duration": 0.010142087936401367, "duration_str": "10.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: storefront.cart", "start": **********.562632, "relative_start": 0.*****************, "end": **********.562632, "relative_end": **********.562632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.app", "start": **********.563887, "relative_start": 0.*****************, "end": **********.563887, "relative_end": **********.563887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.header", "start": **********.564504, "relative_start": 0.035897016525268555, "end": **********.564504, "relative_end": **********.564504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-search", "start": **********.565119, "relative_start": 0.036512136459350586, "end": **********.565119, "relative_end": **********.565119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-cart", "start": **********.567157, "relative_start": 0.03855013847351074, "end": **********.567157, "relative_end": **********.567157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-menu", "start": **********.567662, "relative_start": 0.03905510902404785, "end": **********.567662, "relative_end": **********.567662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-view-modal", "start": **********.568472, "relative_start": 0.03986501693725586, "end": **********.568472, "relative_end": **********.568472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-add-cart-modal", "start": **********.569223, "relative_start": 0.04061603546142578, "end": **********.569223, "relative_end": **********.569223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-wishlist-modal", "start": **********.569636, "relative_start": 0.04102921485900879, "end": **********.569636, "relative_end": **********.569636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.footer", "start": **********.570951, "relative_start": 0.042344093322753906, "end": **********.570951, "relative_end": **********.570951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 3425984, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "storefront.cart", "param_count": null, "params": [], "start": **********.562585, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/cart.blade.phpstorefront.cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fcart.blade.php&line=1", "ajax": false, "filename": "cart.blade.php", "line": "?"}}, {"name": "storefront.layouts.app", "param_count": null, "params": [], "start": **********.563831, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/app.blade.phpstorefront.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "storefront.partials.header", "param_count": null, "params": [], "start": **********.564448, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/header.blade.phpstorefront.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-search", "param_count": null, "params": [], "start": **********.565074, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-search.blade.phpstorefront.partials.aside-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-search.blade.php&line=1", "ajax": false, "filename": "aside-search.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-cart", "param_count": null, "params": [], "start": **********.567104, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-cart.blade.phpstorefront.partials.aside-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-cart.blade.php&line=1", "ajax": false, "filename": "aside-cart.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-menu", "param_count": null, "params": [], "start": **********.567626, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-menu.blade.phpstorefront.partials.aside-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-menu.blade.php&line=1", "ajax": false, "filename": "aside-menu.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-view-modal", "param_count": null, "params": [], "start": **********.568433, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-view-modal.blade.phpstorefront.layouts.quick-view-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-view-modal.blade.php&line=1", "ajax": false, "filename": "quick-view-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-add-cart-modal", "param_count": null, "params": [], "start": **********.56918, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-add-cart-modal.blade.phpstorefront.layouts.quick-add-cart-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-add-cart-modal.blade.php&line=1", "ajax": false, "filename": "quick-add-cart-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-wishlist-modal", "param_count": null, "params": [], "start": **********.569594, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-wishlist-modal.blade.phpstorefront.layouts.quick-wishlist-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-wishlist-modal.blade.php&line=1", "ajax": false, "filename": "quick-wishlist-modal.blade.php", "line": "?"}}, {"name": "storefront.partials.footer", "param_count": null, "params": [], "start": **********.570838, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/footer.blade.phpstorefront.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00184, "accumulated_duration_str": "1.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.554424, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.5547638, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/cart", "action_name": "cart.index", "controller_action": "App\\Http\\Controllers\\Storefront\\CartController@index", "uri": "GET cart", "controller": "App\\Http\\Controllers\\Storefront\\CartController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FCartController.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FCartController.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/CartController.php:32-36</a>", "middleware": "web", "duration": "43.4ms", "peak_memory": "4MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-977713048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-977713048\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-368754275 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-368754275\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-761437840 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">http://mint-cosmetics.local/products/free-shipping1212</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik9xZ2NyNEVOMmRYdEN0U2dNa1dra0E9PSIsInZhbHVlIjoiVDBsYkR0M1ladUk3WThUNWYwcE84RDlEUmMvL1hOT04wdjlWT2o3U1Q0NlJjOTlMWkNMa1Ezby9oUWh2VFdiMG4yUjVHZW9lczVtVmt0b0tRZnMyZ1dVSGhyZE1tSEU3dzhBNjd6bk1VR1owaGNGVHhQTmw4NktCcWJPemhyb1ciLCJtYWMiOiIyOWYxYWQzMjM2ODE3NGQ3NWE3YTI5YTY4MjI5M2FkOTc1ZmQzMWM0NjM4MDI0NWE2MjYyYjUyYmMzZWNkMmJiIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6ImFOYjNFSGl0Rkl5YkcxcVJtNlprdEE9PSIsInZhbHVlIjoiVjdEYkUydkpXdnR6T3NZRDNzU29namV2d1YzVHM5REIrUUVhcE52amRXdzhNelplRnAvL1lXRjZieVNCMGd0ZlRXWGxXOXNmWjNpTXorc1Y3Qk1pcENxTkpiTU5RZExpQjhhS2RmVGdVUlRtZVg2S2JCNkxhTTFoZzdDQXgzcTAiLCJtYWMiOiIxNGYyMWIwYjc0MDUwYjhlMzMwNDBhNmI5MTQyY2UwMjY4ZTk3OTBkNmQzYWRjNDI0ODExYWIxMWM5YWI3ZDRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761437840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-922922522 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922922522\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-96353441 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 15:15:58 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96353441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-961578759 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://mint-cosmetics.local/products/8/quick-view</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961578759\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/cart", "action_name": "cart.index", "controller_action": "App\\Http\\Controllers\\Storefront\\CartController@index"}, "badge": null}}