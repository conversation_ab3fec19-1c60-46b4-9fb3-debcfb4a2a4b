{"__meta": {"id": "01K409WSDB44C1H3SSY2P0HXHH", "datetime": "2025-08-31 14:53:24", "utime": **********.779831, "method": "POST", "uri": "/cart/add", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.741996, "end": **********.779842, "duration": 0.03784584999084473, "duration_str": "37.85ms", "measures": [{"label": "Booting", "start": **********.741996, "relative_start": 0, "end": **********.762171, "relative_end": **********.762171, "duration": 0.*****************, "duration_str": "20.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.762181, "relative_start": 0.020184993743896484, "end": **********.779843, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "17.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.76464, "relative_start": 0.**************, "end": **********.764964, "relative_end": **********.764964, "duration": 0.00032401084899902344, "duration_str": "324μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.779414, "relative_start": 0.*****************, "end": **********.779489, "relative_end": **********.779489, "duration": 7.510185241699219e-05, "duration_str": "75μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3374888, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0036600000000000005, "accumulated_duration_str": "3.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.767276, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.767668, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 60.109}, {"sql": "select count(*) as aggregate from `product_variants` where `id` = '61'", "type": "query", "params": [], "bindings": ["61"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1016}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 987}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.772314, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 60.109, "width_percent": 10.109}, {"sql": "select * from `product_variants` where `product_variants`.`id` = 61 limit 1", "type": "query", "params": [], "bindings": [61], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Storefront/CartService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Storefront\\CartService.php", "line": 20}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Storefront/CartController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\CartController.php", "line": 82}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.774567, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CartService.php:20", "source": {"index": 18, "namespace": null, "name": "app/Services/Storefront/CartService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Storefront\\CartService.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FStorefront%2FCartService.php&line=20", "ajax": false, "filename": "CartService.php", "line": "20"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 70.219, "width_percent": 15.574}, {"sql": "select * from `products` where `products`.`id` in (9) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "app/Services/Storefront/CartService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Storefront\\CartService.php", "line": 20}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/CartController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\CartController.php", "line": 82}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.777447, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CartService.php:20", "source": {"index": 23, "namespace": null, "name": "app/Services/Storefront/CartService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Storefront\\CartService.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FStorefront%2FCartService.php&line=20", "ajax": false, "filename": "CartService.php", "line": "20"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 85.792, "width_percent": 14.208}]}, "models": {"data": {"App\\Models\\ProductVariant": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/cart/add", "action_name": "cart.add", "controller_action": "App\\Http\\Controllers\\Storefront\\CartController@add", "uri": "POST cart/add", "controller": "App\\Http\\Controllers\\Storefront\\CartController@add<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FCartController.php&line=74\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FCartController.php&line=74\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/CartController.php:74-90</a>", "middleware": "web", "duration": "36.77ms", "peak_memory": "4MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1900967809 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1900967809\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1801692878 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>variant_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">61</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801692878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1132382943 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mint-cosmetics.local/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJEQ0FWeGNZak1yK3VSeGFWT0ZHK3c9PSIsInZhbHVlIjoiMnZrU0k3TWRRWWViVnRpQzRtMnpQU2FuM3FzZFBicjFhQjdnNXV0cjFZUmE3Rkc4NDJMS20zeEF0MUlYc2R6blVGcm5vVU9zYURFbGhFZTNpeUk4eStTdmFtd0Fxb0dPQ052SjlHUWJwMk5qdFc1RjJMdnU5K2QrNDZETkl0U0UiLCJtYWMiOiJmZWRmZTVhN2ZkN2M1YjM4MDMzZDUxNDI2OWZkNzI1Y2Y2OGNkY2UwNmI4MzA2N2IzZTBjZjVmMTcxNjdkZWJlIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6Ik1xdmF3RG9hREIyVTBiZzZhQTA5Tnc9PSIsInZhbHVlIjoibmFUdkVNc0dsQldHOERaTmZTdWhONFpLMHExTnRwYnZyekxldU9ZVmVEMGpBSjBuR3hRdjB6aVp4UWdEMEU4UXJUU1ZCVkwwVnBPeERHZ0lGVG1CYVZERitzSjZmS0JOUE9CcGdGN2pZTlQ5VExyZmd3WUk1STZCc1pTNTlwSkgiLCJtYWMiOiIwN2RjYWEwNDliZDc2MTcyZGQ2MGRiODA5NGJlODkyMDQ5ZDcwMDJhOTdlZThmZDU1YWViMDU3ZmNiOGJmNzY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132382943\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1784380201 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784380201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2031881400 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:53:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031881400\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1509208652 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://mint-cosmetics.local/products/9/quick-view</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>16</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">12222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n    <span class=sf-dump-key>61</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>61</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">scscscsc / cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">122222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>62</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>62</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">cscacasc / dwdwd</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">111111.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509208652\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/cart/add", "action_name": "cart.add", "controller_action": "App\\Http\\Controllers\\Storefront\\CartController@add"}, "badge": null}}