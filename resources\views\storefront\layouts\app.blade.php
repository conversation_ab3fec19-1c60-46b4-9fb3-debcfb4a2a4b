<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Brancy - Cosmetic & Beauty Salon Website Template</title>
    <meta name="robots" content="noindex, follow" />
    <meta name="description" content="Brancy - Cosmetic & Beauty Salon Website Template">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="keywords"
        content="bootstrap, ecommerce, ecommerce html, beauty, cosmetic shop, beauty products, cosmetic, beauty shop, cosmetic store, shop, beauty store, spa, cosmetic, cosmetics, beauty salon" />
    <meta name="author" content="codecarnival" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="{{asset('assets/storefront/images/favicon.webp')}}">

    <!-- CSS (Font, Vendor, Icon, Plugins & Style CSS files) -->

    <!-- Font CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap"
        rel="stylesheet">

    <!-- Vendor CSS (Bootstrap & Icon Font) -->
    <link rel="stylesheet" href="{{asset('assets/storefront/css/vendor/bootstrap.min.css')}}">

    <!-- Plugins CSS (All Plugins Files) -->
    <link rel="stylesheet" href="{{asset('assets/storefront/css/plugins/swiper-bundle.min.css')}}">
    <link rel="stylesheet" href="{{asset('assets/storefront/css/plugins/font-awesome.min.css')}}">
    <link rel="stylesheet" href="{{asset('assets/storefront/css/plugins/fancybox.min.css')}}">
    <link rel="stylesheet" href="{{asset('assets/storefront/css/plugins/range-slider.css')}}">
    <link rel="stylesheet" href="{{asset('assets/storefront/css/plugins/nice-select.css')}}">

    <!-- Style CSS -->
    <link rel="stylesheet" href="{{asset('assets/storefront/css/style.min.css')}}">

    <!-- SweetAlert2 CSS -->

</head>

<body>
    <!--== Wrapper Start ==-->
    <div class="wrapper">
        @include('storefront.partials.header')
        @include('storefront.partials.aside-search')
        @include('storefront.partials.aside-cart')
        @include('storefront.partials.aside-menu')
        @include('storefront.layouts.quick-view-modal')
        @include('storefront.layouts.quick-add-cart-modal')
        @include('storefront.layouts.quick-wishlist-modal')
        @yield('content')
        <!--== Scroll Top Button ==-->
        <div id="scroll-to-top" class="scroll-to-top"><span class="fa fa-angle-up"></span></div>

        @include('storefront.partials.footer')
    </div>

    <script src="{{asset('assets/storefront/js/vendor/modernizr-3.11.7.min.js')}}"></script>
    <script src="{{asset('assets/storefront/js/vendor/jquery-3.6.0.min.js')}}"></script>
    <script src="{{asset('assets/storefront/js/vendor/jquery-migrate-3.3.2.min.js')}}"></script>
    <script src="{{asset('assets/storefront/js/vendor/bootstrap.bundle.min.js')}}"></script>

    <!-- Plugins JS -->
    <script src="{{asset('assets/storefront/js/plugins/swiper-bundle.min.js')}}"></script>
    <script src="{{asset('assets/storefront/js/plugins/fancybox.min.js')}}"></script>
    <script src="{{asset('assets/storefront/js/plugins/range-slider.js')}}"></script>
    <script src="{{asset('assets/storefront/js/plugins/jquery.nice-select.min.js')}}"></script>
    <!-- Custom Main JS -->
    <script src="{{asset('assets/storefront/js/main.js')}}"></script>
    <!-- SweetAlert2 JS -->
    <script src="{{asset('assets/storefront/js/cart.js')}}"></script>
    @stack('scripts')

    <!-- Test Script -->
    <script>
        console.log('=== SCRIPT LOADED ===');
        console.log('jQuery loaded:', typeof $);
        console.log('Bootstrap loaded:', typeof bootstrap);

        // Test basic click
        document.addEventListener('click', function(e) {
            console.log('GLOBAL CLICK:', e.target);
        });

        // Test DOM ready + Quick View Logic
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DOM READY ===');
            console.log('Quick view modal:', document.getElementById('action-QuickViewModal'));
            console.log('Quick view container:', document.getElementById('quick-view-container'));

            const quickViewBtns = document.querySelectorAll('.action-btn-quick-view');
            console.log('Quick view buttons found:', quickViewBtns.length);

            if (quickViewBtns.length > 0) {
                console.log('First button:', quickViewBtns[0]);
                console.log('First button product ID:', quickViewBtns[0].dataset.productId);
            }

            // === QUICK VIEW LOGIC ===
            const quickViewModalElement = document.getElementById('action-QuickViewModal');
            if (!quickViewModalElement) {
                console.error('Quick view modal element not found!');
                return;
            }

            const quickViewModal = new bootstrap.Modal(quickViewModalElement);
            const quickViewContainer = document.getElementById('quick-view-container');
            let currentProductVariants = [];

            console.log('Quick view modal initialized');

            // Quick View Event Listener
            document.body.addEventListener('click', function(event) {
                console.log('=== CLICK EVENT ===', event.target);

                // Handle Quick View
                const quickViewButton = event.target.closest('.action-btn-quick-view');
                console.log('Quick view button found:', quickViewButton);

                if (quickViewButton) {
                    console.log('=== QUICK VIEW CLICKED ===');
                    event.preventDefault();
                    const productId = quickViewButton.dataset.productId;

                    console.log('Product ID:', productId);

                    if (!productId) {
                        console.error('Product ID not found');
                        alert('Product ID not found');
                        return;
                    }

                    quickViewContainer.innerHTML = '<p class="text-center">Loading product details...</p>';
                    quickViewModal.show();

                    console.log('Fetching product data from:', `/products/${productId}/quick-view`);

                    fetch(`/products/${productId}/quick-view`)
                        .then(response => {
                            console.log('Response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(product => {
                            console.log('Product data received:', product);
                            console.log('Product type:', typeof product);
                            console.log('Product keys:', Object.keys(product || {}));
                            console.log('Product name:', product?.name);
                            console.log('Product variants:', product?.variants);

                            // Check if product has required data
                            if (!product) {
                                throw new Error('No product data received');
                            }

                            if (!product.name) {
                                console.error('Product missing name field. Full product data:', product);
                                throw new Error('Product missing name field');
                            }

                            // Get first variant for price
                            const firstVariant = product.variants && product.variants.length > 0 ? product.variants[0] : null;
                            const price = firstVariant ? firstVariant.price : 'N/A';
                            const variantId = firstVariant ? firstVariant.id : '';

                            console.log('Rendering product:', {
                                name: product.name,
                                price: price,
                                variantId: variantId,
                                hasImage: !!product.image
                            });

                            // Simple render
                            quickViewContainer.innerHTML = `
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="product-single-thumb">
                                            <img src="${product.image ? '/storage/' + product.image : '/assets/storefront/images/shop/1.webp'}"
                                                 alt="${product.name}" style="width: 100%; height: auto;">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="product-details-content">
                                            <h3 class="product-details-title">${product.name}</h3>
                                            <div class="product-details-review mb-3">
                                                <div class="product-review-icon">
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star-o"></i>
                                                </div>
                                                <span class="ms-2">150 reviews</span>
                                            </div>
                                            <p class="mb-3">${product.description || 'No description available'}</p>
                                            <div class="product-details-action">
                                                <h4 class="price mb-3">Price: ${price} VNĐ</h4>
                                                <div class="product-details-pro-qty mb-3">
                                                    <label class="form-label">Quantity:</label>
                                                    <input type="number" id="quickViewQuantity" value="1" min="1" class="form-control" style="width: 80px; display: inline-block;">
                                                </div>
                                                <button type="button" class="btn btn-primary action-btn-cart"
                                                        data-variant-id="${variantId}"
                                                        data-quantity="1">
                                                    Add to cart
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            console.log('Quick view content rendered successfully');
                        })
                        .catch(error => {
                            console.error('Quick View Error:', error);
                            quickViewContainer.innerHTML = '<p class="text-center text-danger">Could not load product details. Error: ' + error.message + '</p>';
                        });
                    return;
                }

                // Handle Add to Cart
                const addToCartButton = event.target.closest('.action-btn-cart');
                if (addToCartButton) {
                    console.log('=== ADD TO CART CLICKED ===');
                    event.preventDefault();
                    const variantId = addToCartButton.dataset.variantId;
                    const quantity = addToCartButton.dataset.quantity || 1;

                    console.log('Add to cart:', {
                        variantId,
                        quantity
                    });

                    if (window.addToCart) {
                        window.addToCart(variantId, quantity, addToCartButton);
                    } else {
                        console.error('addToCart function not found');
                    }
                    return;
                }
            });
        });
    </script>

    @push('scripts')
    <!-- Old scripts removed - moved to main script above -->
    <script>
        // Old quick view logic removed - now handled in main script above
        // All quick view logic moved to main script above
        console.log('Push script loaded - but all logic moved to main script');
    </script>
    @endpush
</body>


</html>