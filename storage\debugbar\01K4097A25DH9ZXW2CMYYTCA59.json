{"__meta": {"id": "01K4097A25DH9ZXW2CMYYTCA59", "datetime": "2025-08-31 14:41:40", "utime": **********.933188, "method": "PUT", "uri": "/admin/products/10", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.826632, "end": **********.933198, "duration": 0.10656595230102539, "duration_str": "107ms", "measures": [{"label": "Booting", "start": **********.826632, "relative_start": 0, "end": **********.850292, "relative_end": **********.850292, "duration": 0.023659944534301758, "duration_str": "23.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.850301, "relative_start": 0.023669004440307617, "end": **********.933199, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "82.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.852748, "relative_start": 0.026115894317626953, "end": **********.853236, "relative_end": **********.853236, "duration": 0.00048804283142089844, "duration_str": "488μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.932815, "relative_start": 0.*****************, "end": **********.932908, "relative_end": **********.932908, "duration": 9.298324584960938e-05, "duration_str": "93μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3801184, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 20, "nb_statements": 17, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029940000000000005, "accumulated_duration_str": "29.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.85603, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.856404, "duration": 0.01408, "duration_str": "14.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 47.027}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.873176, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 47.027, "width_percent": 1.202}, {"sql": "update `users` set `last_login_at` = '2025-08-31 14:41:40', `users`.`updated_at` = '2025-08-31 14:41:40' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-08-31 14:41:40", "2025-08-31 14:41:40", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/LogSuccessfulLogin.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Listeners\\LogSuccessfulLogin.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 806}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.875875, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "LogSuccessfulLogin.php:46", "source": {"index": 14, "namespace": null, "name": "app/Listeners/LogSuccessfulLogin.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Listeners\\LogSuccessfulLogin.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FListeners%2FLogSuccessfulLogin.php&line=46", "ajax": false, "filename": "LogSuccessfulLogin.php", "line": "46"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 48.23, "width_percent": 4.977}, {"sql": "select * from `products` where `id` = '10' and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.881048, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 53.206, "width_percent": 2.538}, {"sql": "select count(*) as aggregate from `products` where `name` = 'Trà Phạm1212' and `id` <> '10'", "type": "query", "params": [], "bindings": ["Trà Phạm1212", "10"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1061}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.8895822, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 55.745, "width_percent": 3.407}, {"sql": "select count(*) as aggregate from `categories` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1016}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 987}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.892685, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 59.152, "width_percent": 1.369}, {"sql": "select count(*) as aggregate from `brands` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1016}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 987}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.8944871, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 60.521, "width_percent": 1.637}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.901099, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ProductService.php:77", "source": {"index": 10, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=77", "ajax": false, "filename": "ProductService.php", "line": "77"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 62.158, "width_percent": 0}, {"sql": "update `products` set `category_id` = '1', `products`.`updated_at` = '2025-08-31 14:41:40' where `id` = 10", "type": "query", "params": [], "bindings": ["1", "2025-08-31 14:41:40", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 105}, {"index": 19, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.9016418, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:105", "source": {"index": 15, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=105", "ajax": false, "filename": "ProductService.php", "line": "105"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 62.158, "width_percent": 7.749}, {"sql": "select exists(select * from `product_variants` where `product_variants`.`product_id` = 10 and `product_variants`.`product_id` is not null and exists (select * from `order_items` where `product_variants`.`id` = `order_items`.`product_variant_id`)) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 108}, {"index": 18, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.90683, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:108", "source": {"index": 14, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=108", "ajax": false, "filename": "ProductService.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 69.906, "width_percent": 16.166}, {"sql": "delete from `product_variants` where `product_variants`.`product_id` = 10 and `product_variants`.`product_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 113}, {"index": 19, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.912929, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:113", "source": {"index": 15, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=113", "ajax": false, "filename": "ProductService.php", "line": "113"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 86.072, "width_percent": 5.043}, {"sql": "insert into `product_variants` (`price`, `stock`, `discount_price`, `product_id`, `updated_at`, `created_at`) values ('********.00', '50', '********.00', 10, '2025-08-31 14:41:40', '2025-08-31 14:41:40')", "type": "query", "params": [], "bindings": ["********.00", "50", "********.00", 10, "2025-08-31 14:41:40", "2025-08-31 14:41:40"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 130}, {"index": 22, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.916394, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:130", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=130", "ajax": false, "filename": "ProductService.php", "line": "130"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 91.116, "width_percent": 2.138}, {"sql": "insert into `product_variants` (`price`, `stock`, `discount_price`, `product_id`, `updated_at`, `created_at`) values ('1111111.00', '43', '1111111.00', 10, '2025-08-31 14:41:40', '2025-08-31 14:41:40')", "type": "query", "params": [], "bindings": ["1111111.00", "43", "1111111.00", 10, "2025-08-31 14:41:40", "2025-08-31 14:41:40"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 130}, {"index": 22, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.918485, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:130", "source": {"index": 18, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=130", "ajax": false, "filename": "ProductService.php", "line": "130"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 93.253, "width_percent": 1.202}, {"sql": "select * from `attribute_value_product_variant` where `attribute_value_product_variant`.`product_variant_id` = 59", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, {"index": 20, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.9204192, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:133", "source": {"index": 16, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=133", "ajax": false, "filename": "ProductService.php", "line": "133"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 94.456, "width_percent": 1.503}, {"sql": "insert into `attribute_value_product_variant` (`attribute_value_id`, `product_variant_id`) values (11, 59)", "type": "query", "params": [], "bindings": [11, 59], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, {"index": 17, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.921998, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:133", "source": {"index": 13, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=133", "ajax": false, "filename": "ProductService.php", "line": "133"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 95.959, "width_percent": 0.735}, {"sql": "insert into `attribute_value_product_variant` (`attribute_value_id`, `product_variant_id`) values (8, 59)", "type": "query", "params": [], "bindings": [8, 59], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, {"index": 17, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.923779, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:133", "source": {"index": 13, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=133", "ajax": false, "filename": "ProductService.php", "line": "133"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 96.693, "width_percent": 1.403}, {"sql": "select * from `products` where `id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 138}, {"index": 21, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.925436, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:138", "source": {"index": 17, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=138", "ajax": false, "filename": "ProductService.php", "line": "138"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 98.096, "width_percent": 1.035}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 138}, {"index": 26, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.926864, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:138", "source": {"index": 22, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=138", "ajax": false, "filename": "ProductService.php", "line": "138"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 99.132, "width_percent": 0.868}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Admin/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Admin\\ProductController.php", "line": 158}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.932333, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ProductService.php:77", "source": {"index": 9, "namespace": null, "name": "app/Services/Admin/ProductService.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Services\\Admin\\ProductService.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FServices%2FAdmin%2FProductService.php&line=77", "ajax": false, "filename": "ProductService.php", "line": "77"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\ProductVariant": {"created": 2, "retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 2, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 5, "updated": 2, "created": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mint-cosmetics.local/admin/products/10", "action_name": "admin.products.update", "controller_action": "App\\Http\\Controllers\\Admin\\ProductController@update", "uri": "PUT admin/products/{product}", "controller": "App\\Http\\Controllers\\Admin\\ProductController@update<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FAdmin%2FProductController.php&line=152\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FAdmin%2FProductController.php&line=152\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/ProductController.php:152-166</a>", "middleware": "web, auth", "duration": "106ms", "peak_memory": "6MB", "response": "Redirect to http://mint-cosmetics.local/admin/products", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-261213627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-261213627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-220242714 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">variable</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>brand_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cscscscscs</span>\"\n  \"<span class=sf-dump-key>active</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>variants</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">********.00</span>\"\n      \"<span class=sf-dump-key>discount_price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">********.00</span>\"\n      \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"2 characters\">50</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>new_variants</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>attribute_value_ids</span>\" => \"<span class=sf-dump-str title=\"4 characters\">11,8</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1111111.00</span>\"\n      \"<span class=sf-dump-key>discount_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1111111.00</span>\"\n      \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220242714\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-713402832 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2017</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarybGW4NA4egCobzosa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">http://mint-cosmetics.local/admin/products/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im1yTnVTUWpUUHBPUEhlbUxUTXJ1ZFE9PSIsInZhbHVlIjoiU1pEQmljOXBLZTVBbWNXTC93SWMrb1RicG1CSi9HL0JscnVoalRJRlJveDFja2FkaEpTcHRyMXEwNDdSYmIwRlpSdTVVUUw5S2lmR1dMNFQzY0dtdGd5d1hIMmJscE1wcmc4aEs5amQzRnBab0c1c21vNkl5c1VPSXJPditveEkiLCJtYWMiOiIwOWIyMTQ2ZjYzYWExOWNkMWY0ODVkNzJkNDE3NjhkZGQyOTA4MjVjMWM0ZTk5Y2I1NWE5YTdmMTBkMTBjOGIxIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6IldTZmh1dnQ5REgrMnRjRGtxazdEQ0E9PSIsInZhbHVlIjoiSzMrbXJuK0lpRmNoeFdjelhEbXVwNkoyMk1vaEJBckJKQk0xUTRiYlVBV2hwcmZ4djQyejVCNG5UcFFiQVpDRktYbEdaRlN2cjNTVkFrNHlRZVlWQWlIMkJPN0dLblZkczhWQlA1VVFKRUhkcWhUWlJnTGt0eGd0UExuS3owbFoiLCJtYWMiOiJmMWM2N2ZlZjQyMjhlOWYzNzc3ODBkYThmZjE0NjBlMjg1M2U3MzYwZjM1YmNiMmUyMTY5ZTQyNDVhZTAyNGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713402832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-257595874 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257595874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1927028296 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:41:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://mint-cosmetics.local/admin/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927028296\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-404368470 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"57 characters\">http://mint-cosmetics.local/admin/categories/1/attributes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>16</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">********.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"30 characters\"> Product updated successfully.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404368470\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mint-cosmetics.local/admin/products/10", "action_name": "admin.products.update", "controller_action": "App\\Http\\Controllers\\Admin\\ProductController@update"}, "badge": "302 Found"}}