@extends('admin.layouts.app')

@section('content')
    <div class="container-xxl">
        <form method="POST" action="{{ route('admin.categories.store') }}">
            @csrf
            <div class="row">
                <div class="col-xl-3 col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="mt-3">
                                <h4>New Category</h4>
                                <p class="text-muted">
                                    Fill in the information on the right to create a new product category.
                                </p>
                            </div>
                        </div>
                        <div class="card-footer border-top">
                            <div class="row g-2">
                                <div class="col-lg-6">
                                    {{-- The 'submit' button for the form --}}
                                    <button type="submit" class="btn btn-primary w-100">Create Category</button>
                                </div>
                                <div class="col-lg-6">
                                    <a href="{{ route('admin.categories.index') }}"
                                       class="btn btn-outline-secondary w-100">Cancel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-9 col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">General Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {{-- Category Name --}}
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="category-name" class="form-label">Category Name</label>
                                        <input type="text" id="category-name" name="name"
                                               class="form-control @error('name') is-invalid @enderror"
                                               placeholder="Enter category name" value="{{ old('name') }}" required>
                                        @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                {{-- Active Status --}}
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <div class="form-check form-switch form-switch-success">
                                            <input class="form-check-input" type="checkbox" id="category-active"
                                                   name="active" value="1" checked>
                                            <label class="form-check-label" for="category-active">Active</label>
                                        </div>
                                        @error('active')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                {{-- Attributes Selection --}}
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="attribute-select" class="form-label">Link Attributes</label>
                                        <select class="form-control" id="attribute-select" name="attribute_ids[]"
                                                data-choices multiple>
                                            <option value="">Select attributes...</option>
                                            @foreach ($attributes as $attribute)
                                                <option value="{{ $attribute->id }}">{{ $attribute->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('attribute_ids')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection
