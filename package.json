{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "format": "prettier --write .", "format:check": "prettier --check .", "format:staged": "prettier --write \"{app,bootstrap,config,public,resources,routes}/**/*.{blade.php,php,js,ts,jsx,tsx,css,scss,html,json,yml,yaml}\""}, "devDependencies": {"@prettier/plugin-php": "^0.24.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/vite": "^4.0.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.4.31", "prettier": "^3.6.2", "prettier-plugin-blade": "^2.1.21", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.1.0", "vite": "^7.0.4"}}