<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['product']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['product']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="product-item">
    <div class="product-thumb">
        <a class="d-block" href="<?php echo e(route('products.show', $product->slug)); ?>">
            <img
                src="<?php echo e($product->image ? asset('storage/' . $product->image) : asset('assets/storefront/images/shop/1.webp')); ?>"
                width="370" height="450" alt="<?php echo e($product->name); ?>">
        </a>
        
        
        <div class="product-action">
            <button type="button" class="product-action-btn action-btn-quick-view"
                data-product-id="<?php echo e($product->id); ?>">
                <i class="fa fa-expand"></i>
            </button>
            <?php if($product->variants->isNotEmpty()): ?>
            <button type="button" class="product-action-btn action-btn-cart"
                data-variant-id="<?php echo e($product->variants->first()->id); ?>"
                data-quantity="1">
                <span>Add to cart</span>
            </button>
            <?php else: ?>
            <button type="button" class="product-action-btn" disabled>
                <span>Out of stock</span>
            </button>
            <?php endif; ?>
            <button type="button" class="product-action-btn action-btn-wishlist"
                data-bs-toggle="modal" data-bs-target="#action-WishlistModal">
                <i class="fa fa-heart-o"></i>
            </button>
        </div>
    </div>
    <div class="product-info">
        
        <div class="product-rating">
            <div class="rating">
                <i class="fa fa-star-o"></i><i class="fa fa-star-o"></i><i class="fa fa-star-o"></i><i
                    class="fa fa-star-o"></i><i class="fa fa-star-half-o"></i>
            </div>
            <div class="reviews">150 reviews</div>
        </div>
        <h4 class="title"><a href="<?php echo e(route('products.show', $product->slug)); ?>"><?php echo e($product->name); ?></a></h4>
        <div class="prices">
            <?php $firstVariant = $product->variants->first(); ?>
            <?php if($firstVariant): ?>
            <?php if($firstVariant->discount_price && $firstVariant->discount_price < $firstVariant->price): ?>
                <span class="price"><?php echo e(number_format($firstVariant->discount_price, 0, ',', '.')); ?> VNĐ</span>
                <span class="price-old"><?php echo e(number_format($firstVariant->price, 0, ',', '.')); ?> VNĐ</span>
                <?php else: ?>
                <span class="price"><?php echo e(number_format($firstVariant->price, 0, ',', '.')); ?> VNĐ</span>
                <?php endif; ?>
                <?php endif; ?>
        </div>
    </div>
</div><?php /**PATH C:\wamp64\www\projects\mint_cosmetics\resources\views/components/product-card.blade.php ENDPATH**/ ?>