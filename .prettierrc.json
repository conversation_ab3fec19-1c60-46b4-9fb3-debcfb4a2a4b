{"$schema": "https://json.schemastore.org/prettierrc", "singleQuote": true, "semi": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "trailingComma": "es5", "plugins": ["prettier-plugin-blade", "prettier-plugin-tailwindcss"], "overrides": [{"files": ["*.blade.php"], "options": {"parser": "blade", "singleQuote": false, "htmlWhitespaceSensitivity": "ignore"}}, {"files": ["*.html"], "options": {"parser": "html"}}, {"files": ["*.css", "*.scss"], "options": {"singleQuote": false}}, {"files": ["*.json", "*.yml", "*.yaml"], "options": {"tabWidth": 2}}]}