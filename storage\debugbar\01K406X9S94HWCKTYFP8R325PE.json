{"__meta": {"id": "01K406X9S94HWCKTYFP8R325PE", "datetime": "2025-08-31 14:01:15", "utime": **********.817742, "method": "GET", "uri": "/products/tra-pham1212", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 15, "start": **********.720843, "end": **********.817759, "duration": 0.09691596031188965, "duration_str": "96.92ms", "measures": [{"label": "Booting", "start": **********.720843, "relative_start": 0, "end": **********.755327, "relative_end": **********.755327, "duration": 0.034483909606933594, "duration_str": "34.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.755343, "relative_start": 0.****************, "end": **********.817761, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "62.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.759642, "relative_start": 0.*****************, "end": **********.760367, "relative_end": **********.760367, "duration": 0.0007250308990478516, "duration_str": "725μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.80657, "relative_start": 0.*****************, "end": **********.817289, "relative_end": **********.817289, "duration": 0.010719060897827148, "duration_str": "10.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: storefront.product-detail", "start": **********.807054, "relative_start": 0.*****************, "end": **********.807054, "relative_end": **********.807054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.809964, "relative_start": 0.*****************, "end": **********.809964, "relative_end": **********.809964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.app", "start": **********.813064, "relative_start": 0.09222102165222168, "end": **********.813064, "relative_end": **********.813064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.header", "start": **********.813341, "relative_start": 0.0924978256225586, "end": **********.813341, "relative_end": **********.813341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-search", "start": **********.813616, "relative_start": 0.0927729606628418, "end": **********.813616, "relative_end": **********.813616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-cart", "start": **********.814133, "relative_start": 0.09328985214233398, "end": **********.814133, "relative_end": **********.814133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-menu", "start": **********.814405, "relative_start": 0.09356188774108887, "end": **********.814405, "relative_end": **********.814405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-view-modal", "start": **********.814912, "relative_start": 0.09406900405883789, "end": **********.814912, "relative_end": **********.814912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-add-cart-modal", "start": **********.815356, "relative_start": 0.094512939453125, "end": **********.815356, "relative_end": **********.815356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-wishlist-modal", "start": **********.815572, "relative_start": 0.09472894668579102, "end": **********.815572, "relative_end": **********.815572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.footer", "start": **********.816193, "relative_start": 0.09535002708435059, "end": **********.816193, "relative_end": **********.816193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 3938272, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "storefront.product-detail", "param_count": null, "params": [], "start": **********.806993, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/product-detail.blade.phpstorefront.product-detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fproduct-detail.blade.php&line=1", "ajax": false, "filename": "product-detail.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.809915, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "storefront.layouts.app", "param_count": null, "params": [], "start": **********.813027, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/app.blade.phpstorefront.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "storefront.partials.header", "param_count": null, "params": [], "start": **********.813309, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/header.blade.phpstorefront.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-search", "param_count": null, "params": [], "start": **********.813584, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-search.blade.phpstorefront.partials.aside-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-search.blade.php&line=1", "ajax": false, "filename": "aside-search.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-cart", "param_count": null, "params": [], "start": **********.8141, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-cart.blade.phpstorefront.partials.aside-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-cart.blade.php&line=1", "ajax": false, "filename": "aside-cart.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-menu", "param_count": null, "params": [], "start": **********.814374, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-menu.blade.phpstorefront.partials.aside-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-menu.blade.php&line=1", "ajax": false, "filename": "aside-menu.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-view-modal", "param_count": null, "params": [], "start": **********.814876, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-view-modal.blade.phpstorefront.layouts.quick-view-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-view-modal.blade.php&line=1", "ajax": false, "filename": "quick-view-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-add-cart-modal", "param_count": null, "params": [], "start": **********.815324, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-add-cart-modal.blade.phpstorefront.layouts.quick-add-cart-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-add-cart-modal.blade.php&line=1", "ajax": false, "filename": "quick-add-cart-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-wishlist-modal", "param_count": null, "params": [], "start": **********.815542, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-wishlist-modal.blade.phpstorefront.layouts.quick-wishlist-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-wishlist-modal.blade.php&line=1", "ajax": false, "filename": "quick-wishlist-modal.blade.php", "line": "?"}}, {"name": "storefront.partials.footer", "param_count": null, "params": [], "start": **********.816148, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/footer.blade.phpstorefront.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 9, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019340000000000003, "accumulated_duration_str": "19.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.765059, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.76564, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 75.44}, {"sql": "select * from `products` where `slug` = 'tra-pham1212' and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tra-pham1212"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 980}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.7837172, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 75.44, "width_percent": 3.361}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.787247, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 78.8, "width_percent": 3.102}, {"sql": "select * from `brands` where `brands`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7904189, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 81.903, "width_percent": 3.051}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.792925, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 84.953, "width_percent": 2.947}, {"sql": "select `attribute_values`.*, `attribute_value_product_variant`.`product_variant_id` as `pivot_product_variant_id`, `attribute_value_product_variant`.`attribute_value_id` as `pivot_attribute_value_id` from `attribute_values` inner join `attribute_value_product_variant` on `attribute_values`.`id` = `attribute_value_product_variant`.`attribute_value_id` where `attribute_value_product_variant`.`product_variant_id` in (56)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7961168, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 24, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 87.901, "width_percent": 3.981}, {"sql": "select * from `attributes` where `attributes`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.799439, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:25", "source": {"index": 29, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=25", "ajax": false, "filename": "ProductController.php", "line": "25"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 91.882, "width_percent": 2.896}, {"sql": "select * from `products` where `category_id` = 7 and `id` != 10 and `active` = 1 and `products`.`deleted_at` is null limit 10", "type": "query", "params": [], "bindings": [7, 10, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.801108, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=37", "ajax": false, "filename": "ProductController.php", "line": "37"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 94.778, "width_percent": 2.792}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.803533, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:37", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Storefront/ProductController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ProductController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=37", "ajax": false, "filename": "ProductController.php", "line": "37"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 97.57, "width_percent": 2.43}]}, "models": {"data": {"App\\Models\\Product": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductVariant": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\AttributeValue": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttributeValue.php&line=1", "ajax": false, "filename": "AttributeValue.php", "line": "?"}}, "App\\Models\\Attribute": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}}, "count": 8, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 8}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/tra-pham1212", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\Storefront\\ProductController@show", "uri": "GET products/{product}", "controller": "App\\Http\\Controllers\\Storefront\\ProductController@show<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FProductController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/ProductController.php:22-40</a>", "middleware": "web", "duration": "96.84ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-747892661 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-747892661\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2019157067 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019157067\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1878432334 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJlQVg1UGNabVZ4R3ZXMmVEZDRRdkE9PSIsInZhbHVlIjoiQkNKUjNyUWo1MEVQNWllVmdIRng5V25GQ0NBbk02ckUwL0NWaENpOGNDUUZhVmN4VkV5dExWVmVQM3RYeWtaVTVWSUNHc25QajZFZTI3d3dYcnFrcncyVlJZK05oWEM0T2RuN25IcUd1ZUhsL042aFF1dVMrMUh5MkU1YTNzUjkiLCJtYWMiOiJjMzQxMDUyMGQ2OWExN2JkY2ZkMGVmZGQ4ODYxNDllYzFlMzcxZWI3YTY2YmNlYjA0NTliMTQwNDc1OTI2ZWQ1IiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6InVDU3oxaVJmbmM1VmswVVV5Q2JmaUE9PSIsInZhbHVlIjoiL3JCOVNxVTR4THhvcUJZenFnUVE1RnplWDR2RmgzRTNKY2xVNFZZd2RLaU1hS0ZQY3ozTTUzQUtOM2tVWTVYWWxDRFBVbFlXRUNSMGNIdjFkUnl1eVFNZHZ1eENKd3pnUllFVmFtdURuUkhCZ1I1R1B6TTdwOUJpeEFvQm15SDgiLCJtYWMiOiI2OTM5NTQ2YjI2MDc1YTg2NGRlNDUxNWJlN2ZhZDI5ZjE5NzQ3NjhkZjM5ZmU1YTFkZWViMTYwNjA4YzU3Yjg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878432334\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1094688292 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094688292\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-913309860 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 14:01:15 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913309860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-364967650 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://mint-cosmetics.local/shop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">12222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364967650\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/products/tra-pham1212", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\Storefront\\ProductController@show"}, "badge": null}}