{"__meta": {"id": "01K406C0D8TY8G2VDGKDXE3EZB", "datetime": "2025-08-31 13:51:49", "utime": **********.16104, "method": "GET", "uri": "/shop", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 19, "start": **********.058008, "end": **********.161051, "duration": 0.1030430793762207, "duration_str": "103ms", "measures": [{"label": "Booting", "start": **********.058008, "relative_start": 0, "end": **********.10268, "relative_end": **********.10268, "duration": 0.*****************, "duration_str": "44.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.102694, "relative_start": 0.044686079025268555, "end": **********.161052, "relative_end": 9.5367431640625e-07, "duration": 0.058357954025268555, "duration_str": "58.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.114354, "relative_start": 0.*****************, "end": **********.115095, "relative_end": **********.115095, "duration": 0.0007410049438476562, "duration_str": "741μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.150805, "relative_start": 0.*****************, "end": **********.160545, "relative_end": **********.160545, "duration": 0.009740114212036133, "duration_str": "9.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: storefront.shop", "start": **********.151234, "relative_start": 0.*****************, "end": **********.151234, "relative_end": **********.151234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.152333, "relative_start": 0.*****************, "end": **********.152333, "relative_end": **********.152333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.153762, "relative_start": 0.09575414657592773, "end": **********.153762, "relative_end": **********.153762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.15402, "relative_start": 0.09601211547851562, "end": **********.15402, "relative_end": **********.15402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.product-card", "start": **********.1543, "relative_start": 0.09629201889038086, "end": **********.1543, "relative_end": **********.1543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: vendor.pagination.storefront-pagination", "start": **********.155019, "relative_start": 0.09701108932495117, "end": **********.155019, "relative_end": **********.155019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.app", "start": **********.156277, "relative_start": 0.09826898574829102, "end": **********.156277, "relative_end": **********.156277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.header", "start": **********.156793, "relative_start": 0.0987851619720459, "end": **********.156793, "relative_end": **********.156793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-search", "start": **********.157269, "relative_start": 0.09926104545593262, "end": **********.157269, "relative_end": **********.157269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-cart", "start": **********.157824, "relative_start": 0.09981608390808105, "end": **********.157824, "relative_end": **********.157824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.aside-menu", "start": **********.158139, "relative_start": 0.10013103485107422, "end": **********.158139, "relative_end": **********.158139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-view-modal", "start": **********.158682, "relative_start": 0.10067415237426758, "end": **********.158682, "relative_end": **********.158682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-add-cart-modal", "start": **********.159148, "relative_start": 0.10114002227783203, "end": **********.159148, "relative_end": **********.159148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.layouts.quick-wishlist-modal", "start": **********.159427, "relative_start": 0.10141897201538086, "end": **********.159427, "relative_end": **********.159427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: storefront.partials.footer", "start": **********.159955, "relative_start": 0.10194706916809082, "end": **********.159955, "relative_end": **********.159955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 3699704, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.25.0", "PHP Version": "8.4.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mint-cosmetics.local/", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 15, "nb_templates": 15, "templates": [{"name": "storefront.shop", "param_count": null, "params": [], "start": **********.151196, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/shop.blade.phpstorefront.shop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fshop.blade.php&line=1", "ajax": false, "filename": "shop.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.152301, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.153731, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.153989, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "components.product-card", "param_count": null, "params": [], "start": **********.154244, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/components/product-card.blade.phpcomponents.product-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fcomponents%2Fproduct-card.blade.php&line=1", "ajax": false, "filename": "product-card.blade.php", "line": "?"}}, {"name": "vendor.pagination.storefront-pagination", "param_count": null, "params": [], "start": **********.154974, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/vendor/pagination/storefront-pagination.blade.phpvendor.pagination.storefront-pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fstorefront-pagination.blade.php&line=1", "ajax": false, "filename": "storefront-pagination.blade.php", "line": "?"}}, {"name": "storefront.layouts.app", "param_count": null, "params": [], "start": **********.156232, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/app.blade.phpstorefront.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "storefront.partials.header", "param_count": null, "params": [], "start": **********.156748, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/header.blade.phpstorefront.partials.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-search", "param_count": null, "params": [], "start": **********.157238, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-search.blade.phpstorefront.partials.aside-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-search.blade.php&line=1", "ajax": false, "filename": "aside-search.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-cart", "param_count": null, "params": [], "start": **********.157793, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-cart.blade.phpstorefront.partials.aside-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-cart.blade.php&line=1", "ajax": false, "filename": "aside-cart.blade.php", "line": "?"}}, {"name": "storefront.partials.aside-menu", "param_count": null, "params": [], "start": **********.158107, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/aside-menu.blade.phpstorefront.partials.aside-menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Faside-menu.blade.php&line=1", "ajax": false, "filename": "aside-menu.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-view-modal", "param_count": null, "params": [], "start": **********.15865, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-view-modal.blade.phpstorefront.layouts.quick-view-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-view-modal.blade.php&line=1", "ajax": false, "filename": "quick-view-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-add-cart-modal", "param_count": null, "params": [], "start": **********.159116, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-add-cart-modal.blade.phpstorefront.layouts.quick-add-cart-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-add-cart-modal.blade.php&line=1", "ajax": false, "filename": "quick-add-cart-modal.blade.php", "line": "?"}}, {"name": "storefront.layouts.quick-wishlist-modal", "param_count": null, "params": [], "start": **********.159396, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/layouts/quick-wishlist-modal.blade.phpstorefront.layouts.quick-wishlist-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Flayouts%2Fquick-wishlist-modal.blade.php&line=1", "ajax": false, "filename": "quick-wishlist-modal.blade.php", "line": "?"}}, {"name": "storefront.partials.footer", "param_count": null, "params": [], "start": **********.159925, "type": "blade", "hash": "bladeC:\\wamp64\\www\\projects\\mint_cosmetics\\resources\\views/storefront/partials/footer.blade.phpstorefront.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fresources%2Fviews%2Fstorefront%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01705, "accumulated_duration_str": "17.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.119669, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ' limit 1", "type": "query", "params": [], "bindings": ["sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.120501, "duration": 0.0148, "duration_str": "14.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 0, "width_percent": 86.804}, {"sql": "select count(*) as aggregate from `products` where `active` = 1 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.139429, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=29", "ajax": false, "filename": "ShopController.php", "line": "29"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 86.804, "width_percent": 2.581}, {"sql": "select * from `products` where `active` = 1 and `products`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.141788, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=29", "ajax": false, "filename": "ShopController.php", "line": "29"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 89.384, "width_percent": 3.988}, {"sql": "select * from `product_variants` where `product_variants`.`product_id` in (6, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.1449902, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:29", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=29", "ajax": false, "filename": "ShopController.php", "line": "29"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 93.372, "width_percent": 4.692}, {"sql": "select * from `categories` where `active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 32}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.147398, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ShopController.php:32", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Storefront/ShopController.php", "file": "C:\\wamp64\\www\\projects\\mint_cosmetics\\app\\Http\\Controllers\\Storefront\\ShopController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=32", "ajax": false, "filename": "ShopController.php", "line": "32"}, "connection": "mint_cosmetics", "explain": null, "start_percent": 98.065, "width_percent": 1.935}]}, "models": {"data": {"App\\Models\\Category": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductVariant": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FModels%2FProductVariant.php&line=1", "ajax": false, "filename": "ProductVariant.php", "line": "?"}}}, "count": 13, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 13}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/shop", "action_name": "shop", "controller_action": "App\\Http\\Controllers\\Storefront\\ShopController@index", "uri": "GET shop", "controller": "App\\Http\\Controllers\\Storefront\\ShopController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fprojects%2Fmint_cosmetics%2Fapp%2FHttp%2FControllers%2FStorefront%2FShopController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Storefront/ShopController.php:22-35</a>", "middleware": "web", "duration": "103ms", "peak_memory": "4MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-760271450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-760271450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1241644387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1241644387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2067519114 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">mint-cosmetics.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">http://mint-cosmetics.local/products/free-shipping1212</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en,vi;q=0.9,vi-VN;q=0.8,fr-FR;q=0.7,fr;q=0.6,en-US;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZubmh0MDh2b3VRQ3piclYyWStGQXc9PSIsInZhbHVlIjoiNU1BbW9uUmc5bWdzenZFRGRzYzNReFdVODR3L0NqdTN5c1VsMG9IMy9EdzQ1UmNkOFlSVlhEb1BDb2owNkFLZjE1R1NNajNzZy9mOElCQnF4RzliNEI1OTVvU0llUnRJQXk0a2lKR2lsMi9lcGRCbzRZTWVPYkFGZDFQUDBUS0EiLCJtYWMiOiJlNDExNzVlYzllMThlOGUyN2RkZDk3OGRiODlmNDE1MGI3NGRjMDY3NmQ4N2RlMjA3OTU2Y2UyMTYwYTViY2ZmIiwidGFnIjoiIn0%3D; laravel-session=eyJpdiI6ImxSKzZtbndjd0FoL2FQOEtUR0ZWNFE9PSIsInZhbHVlIjoiUk0zOWh6U1pFOFRENHBpN1lJb1FRWkJWTmV6Z2lkVFI1dk1hNGFIZXpyZHZCUWxCRDFwYkdZSTBmaXh6S2NDL3djUGZXMGhXcHVPZ2RabmZkZFFVSWppdmdWTGFZMW40cFB6MHNqZHcxTVFaUlFMRE5PVVErYUsrNEJoUDJDR3ciLCJtYWMiOiJiMDRlNGE0OWQzYTA1NTQwMWI2YTdiZTZjODc5YjZkNjJiMTUxYjEyNDk1ZGI1ZmYwZGYxMGI1MjQwOGEyYmM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067519114\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-219235160 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>laravel-session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sQzBsgGdTl1EDkfdX7ZQROxPKY3k784628ANzCwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219235160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-82566034 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 31 Aug 2025 13:51:49 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82566034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-677964172 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0t6OHnSo5Hi2lc9DAprWO2u9lmAFN4Ke92vY0n9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://mint-cosmetics.local/products/free-shipping1212</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>57</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>57</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Free Shipping1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">free-shipping1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>13</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">10755555.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/kQGhdLmz94nyyr19yfguWaUbgmHHHYfGkarYeWun.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>53</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>8</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tr&#224; Ph&#7841;m121</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tra-phamddwwd</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">11000000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/LRpHqylyw11maDUIRxiTqgt6LoSvZVos46Puw2CV.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>56</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>56</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tr&#224; Ph&#7841;m1212</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">tra-pham1212</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">cscacasc</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">12222222.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/UVw4y1BbJaTvchDiXaPOAhLG39U0uQRKHjgVWmHJ.jpg</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>variant_id</span>\" => <span class=sf-dump-num>33</span>\n      \"<span class=sf-dump-key>product_id</span>\" => <span class=sf-dump-num>6</span>\n      \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Son YSL Slim Velvet Radical Matte Lipstick 1966 Rouge Libre &#8211; M&#224;u &#272;&#7887; G&#7841;ch</span>\"\n      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"71 characters\">son-ysl-slim-velvet-radical-matte-lipstick-1966-rouge-libre-mau-do-gach</span>\"\n      \"<span class=sf-dump-key>variant_name</span>\" => \"\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">120000.00</span>\"\n      \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"53 characters\">products/fxZrKfosA3PHm1o9YetBbBduJ1ZW3hfeFt37r1jf.png</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677964172\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mint-cosmetics.local/shop", "action_name": "shop", "controller_action": "App\\Http\\Controllers\\Storefront\\ShopController@index"}, "badge": null}}