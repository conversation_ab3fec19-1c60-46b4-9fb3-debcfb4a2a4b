<?php

/**
 * @project mint_cosmetics
 *
 * <AUTHOR>
 *
 * @email <EMAIL>
 *
 * @date 8/22/2025
 *
 * @time 3:27 PM
 */

namespace Database\Factories;

use App\Models\AttributeValue;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttributeValueFactory extends Factory
{
    protected $model = AttributeValue::class;

    public function definition(): array
    {
        return [

        ];
    }
}
