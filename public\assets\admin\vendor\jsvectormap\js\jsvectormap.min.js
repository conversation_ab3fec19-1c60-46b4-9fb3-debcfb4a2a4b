!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).jsVectorMap=e()}(this,(function(){"use strict";Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(t){for(var e=(this.document||this.ownerDocument).querySelectorAll(t),i=e.length;--i>=0&&e.item(i)!==this;);return i>-1}),Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:function(t){if(null==t)throw new TypeError("Cannot convert first argument to object");for(var e=Object(t),i=1;i<arguments.length;i++){var s=arguments[i];if(null!=s){s=Object(s);for(var a=Object.keys(Object(s)),n=0,r=a.length;n<r;n++){var o=a[n],h=Object.getOwnPropertyDescriptor(s,o);void 0!==h&&h.enumerable&&(e[o]=s[o])}}}return e}});var t=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var i=Object.prototype.toString.call(t);return"[object RegExp]"===i||"[object Date]"===i||function(t){return t instanceof Node}(t)||function(t){return t.$$typeof===e}(t)}(t)};var e="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(t,e){return!1!==e.clone&&e.isMergeableObject(t)?o((i=t,Array.isArray(i)?[]:{}),t,e):t;var i}function s(t,e,s){return t.concat(e).map((function(t){return i(t,s)}))}function a(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function n(t,e){try{return e in t}catch(t){return!1}}function r(t,e,s){var r={};return s.isMergeableObject(t)&&a(t).forEach((function(e){r[e]=i(t[e],s)})),a(e).forEach((function(a){(function(t,e){return n(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,a)||(n(t,a)&&s.isMergeableObject(e[a])?r[a]=function(t,e){if(!e.customMerge)return o;var i=e.customMerge(t);return"function"==typeof i?i:o}(a,s)(t[a],e[a],s):r[a]=i(e[a],s))})),r}var o=function(e,a,n){(n=n||{}).arrayMerge=n.arrayMerge||s,n.isMergeableObject=n.isMergeableObject||t,n.cloneUnlessOtherwiseSpecified=i;var o=Array.isArray(a);return o===Array.isArray(e)?o?n.arrayMerge(e,a,n):r(e,a,n):i(a,n)},h=function(t){return"object"==typeof t&&void 0!==t.nodeType?t:"string"==typeof t?document.querySelector(t):null},l=function(t,e,i,s){void 0===s&&(s=!1);var a=document.createElement(t);return i&&(a[s?"innerHTML":"textContent"]=i),e&&(a.className=e),a},c=function(t){t.parentNode.removeChild(t)},u=function(t,e,i){return void 0===i&&(i=!1),i?o(t,e):Object.assign(t,e)},p=function(t,e){return t.toLowerCase()+":to:"+e.toLowerCase()},d=function(t,e){Object.assign(t.prototype,e)},f={},m=1,g={on:function(t,e,i,s){void 0===s&&(s={});var a="jvm:"+e+"::"+m++;f[a]={selector:t,handler:i},i._uid=a,t.addEventListener(e,i,s)},delegate:function(t,e,i,s){(e=e.split(" ")).forEach((function(e){g.on(t,e,(function(t){var e=t.target;e.matches(i)&&s.call(e,t)}))}))},off:function(t,e,i){var s=e.split(":")[1];t.removeEventListener(s,i),delete f[i._uid]},flush:function(){Object.keys(f).forEach((function(t){g.off(f[t].selector,t,f[t].handler)}))},getEventRegistry:function(){return f}};var v={onViewportChange:"viewport:changed",onRegionClick:"region:clicked",onMarkerClick:"marker:clicked",onRegionSelected:"region:selected",onMarkerSelected:"marker:selected",onRegionTooltipShow:"region.tooltip:show",onMarkerTooltipShow:"marker.tooltip:show",onLoaded:"map:loaded",onDestroyed:"map:destroyed"};function y(t,e,i){var s=h(e),a=-1===s.getAttribute("class").indexOf("jvm-region")?"marker":"region",n="region"===a?s.getAttribute("data-code"):s.getAttribute("data-index"),r=a+":selected";return i&&(r=a+".tooltip:show"),{event:r,type:a,code:n,element:"region"===a?t.regions[n].element:t.markers[n].element,tooltipText:"region"===a?t.mapData.paths[n].name||"":t.markers[n].config.name||""}}function b(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,_(t,e)}function _(t,e){return _=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_(t,e)}function w(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function S(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=new Array(e);i<e;i++)s[i]=t[i];return s}function k(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return S(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?S(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var s=0;return function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var x=function(){function t(){}return t.prototype.dispose=function(){this._tooltip?c(this._tooltip):this.shape.remove();for(var t,e=k(Object.getOwnPropertyNames(this));!(t=e()).done;){this[t.value]=null}},t}(),M={getLabelText:function(t,e){if(e){if("function"==typeof e.render){var i=[];return this.config&&this.config.marker&&i.push(this.config.marker),i.push(t),e.render.apply(this,i)}return t}},getLabelOffsets:function(t,e){return"function"==typeof e.offsets?e.offsets(t):Array.isArray(e.offsets)?e.offsets[t]:[0,0]},setStyle:function(t,e){this.shape.setStyle(t,e)},remove:function(){this.shape.remove(),this.label&&this.label.remove()},hover:function(t){this._setStatus("isHovered",t)},select:function(t){this._setStatus("isSelected",t)},_setStatus:function(t,e){this.shape[t]=e,this.shape.updateStyle(),this[t]=e,this.label&&(this.label[t]=e,this.label.updateStyle())}},j=function(t){function e(e){var i,s=e.map,a=e.code,n=e.path,r=e.style,o=e.label,h=e.labelStyle,l=e.labelsGroup;(i=t.call(this)||this)._map=s,i.shape=i._createRegion(n,a,r);var c=i.shape.getBBox(),u=i.getLabelText(a,o);if(o&&u){var p=i.getLabelOffsets(a,o);i.labelX=c.x+c.width/2+p[0],i.labelY=c.y+c.height/2+p[1],i.label=i._map.canvas.createText({text:u,textAnchor:"middle",alignmentBaseline:"central",dataCode:a,x:i.labelX,y:i.labelY},h,l),i.label.addClass("jvm-region jvm-element")}return i}b(e,t);var i=e.prototype;return i._createRegion=function(t,e,i){return(t=this._map.canvas.createPath({d:t,dataCode:e},i)).addClass("jvm-region jvm-element"),t},i.updateLabelPosition=function(){this.label&&this.label.set({x:this.labelX*this._map.scale+this._map.transX*this._map.scale,y:this.labelY*this._map.scale+this._map.transY*this._map.scale})},e}(x);d(j,M);var C=function(t){function e(e){var i,s=e.index,a=e.map,n=e.style,r=e.x1,o=e.y1,h=e.x2,l=e.y2,c=e.group,u=e.config;return(i=t.call(this)||this).config=u,i.shape=a.canvas.createLine({x1:r,y1:o,x2:h,y2:l,dataIndex:s},n,c),i.shape.addClass("jvm-line"),i}return b(e,t),e.prototype.setStyle=function(t,e){this.shape.setStyle(t,e)},e}(x);var L=function(t){function e(e){var i,s=e.index,a=e.style,n=e.label,r=e.cx,o=e.cy,h=e.map,l=e.group;return(i=t.call(this)||this)._map=h,i._isImage=!!a.initial.image,i.config=arguments[0],i.shape=h.canvas[i._isImage?"createImage":"createCircle"]({dataIndex:s,cx:r,cy:o},a,l),i.shape.addClass("jvm-marker jvm-element"),i._isImage&&i.updateLabelPosition(),n&&i._createLabel(i.config),i}b(e,t);var i=e.prototype;return i.updateLabelPosition=function(){this.label&&this.label.set({x:this._labelX*this._map.scale+this._offsets[0]+this._map.transX*this._map.scale+5+(this._isImage?(this.shape.width||0)/2:this.shape.node.r.baseVal.value),y:this._labelY*this._map.scale+this._map.transY*this._map.scale+this._offsets[1]})},i._createLabel=function(t){var e=t.index,i=t.map,s=t.label,a=t.labelsGroup,n=t.cx,r=t.cy,o=t.marker,h=t.isRecentlyCreated,l=this.getLabelText(e,s);this._labelX=n/i.scale-i.transX,this._labelY=r/i.scale-i.transY,this._offsets=h&&o.offsets?o.offsets:this.getLabelOffsets(e,s),this.label=i.canvas.createText({text:l,dataIndex:e,x:this._labelX,y:this._labelY,dy:"0.6ex"},i.params.markerLabelStyle,a),this.label.addClass("jvm-marker jvm-element"),h&&this.updateLabelPosition()},e}(x);d(L,M);var O=function(){function t(t){void 0===t&&(t={}),this._options=t,this._map=this._options.map,this._series=this._options.series,this._body=l("div","jvm-legend"),this._options.cssClass&&this._body.setAttribute("class",this._options.cssClass),t.vertical?this._map.legendVertical.appendChild(this._body):this._map.legendHorizontal.appendChild(this._body),this.render()}return t.prototype.render=function(){var t,e,i,s,a=this._series.scale.getTicks(),n=l("div","jvm-legend-inner");if(this._body.innderHTML="",this._options.title){var r=l("div","jvm-legend-title",this._options.title);this._body.appendChild(r)}this._body.appendChild(n);for(var o=0;o<a.length;o++){switch(t=l("div","jvm-legend-tick"),e=l("div","jvm-legend-tick-sample"),this._series.config.attribute){case"fill":s=a[o].value,/\.(jpg|gif|png)$/.test(s)?e.style.background="url("+a[o].value+")":e.style.background=a[o].value;break;case"stroke":e.style.background=a[o].value;break;case"image":e.style.background="url("+("object"==typeof a[o].value?a[o].value.url:a[o].value)+") no-repeat center center",e.style.backgroundSize="cover"}t.appendChild(e),i=a[o].label,this._options.labelRender&&(i=this._options.labelRender(i));var h=l("div","jvm-legend-tick-text",i);t.appendChild(h),n.appendChild(t)}},t}(),A=function(){function t(t){this._scale=t}var e=t.prototype;return e.getValue=function(t){return this._scale[t]},e.getTicks=function(){var t=[];for(var e in this._scale)t.push({label:e,value:this._scale[e]});return t},t}(),X=function(){function t(t,e,i){void 0===t&&(t={}),this._map=i,this._elements=e,this._values=t.values||{},this.config=t,this.config.attribute=t.attribute||"fill",t.attributes&&this.setAttributes(t.attributes),"object"==typeof t.scale&&(this.scale=new A(t.scale)),this.config.legend&&(this.legend=new O(u({map:this._map,series:this},this.config.legend))),this.setValues(this._values)}var e=t.prototype;return e.setValues=function(t){var e={};for(var i in t)t[i]&&(e[i]=this.scale.getValue(t[i]));this.setAttributes(e)},e.setAttributes=function(t){for(var e in t)this._elements[e]&&this._elements[e].element.setStyle(this.config.attribute,t[e])},e.clear=function(){var t,e={};for(t in this._values)this._elements[t]&&(e[t]=this._elements[t].element.shape.style.initial[this.config.attribute]);this.setAttributes(e),this._values={}},t}();var Y={mill:function(t,e,i){return{x:this.radius*(e-i)*this.radDeg,y:-this.radius*Math.log(Math.tan((45+.4*t)*this.radDeg))/.8}},merc:function(t,e,i){return{x:this.radius*(e-i)*this.radDeg,y:-this.radius*Math.log(Math.tan(Math.PI/4+t*Math.PI/360))}},aea:function(t,e,i){var s=i*this.radDeg,a=29.5*this.radDeg,n=45.5*this.radDeg,r=t*this.radDeg,o=e*this.radDeg,h=(Math.sin(a)+Math.sin(n))/2,l=Math.cos(a)*Math.cos(a)+2*h*Math.sin(a),c=h*(o-s),u=Math.sqrt(l-2*h*Math.sin(r))/h,p=Math.sqrt(l-2*h*Math.sin(0))/h;return{x:u*Math.sin(c)*this.radius,y:-(p-u*Math.cos(c))*this.radius}},lcc:function(t,e,i){var s=i*this.radDeg,a=e*this.radDeg,n=33*this.radDeg,r=45*this.radDeg,o=t*this.radDeg,h=Math.log(Math.cos(n)*(1/Math.cos(r)))/Math.log(Math.tan(Math.PI/4+r/2)*(1/Math.tan(Math.PI/4+n/2))),l=Math.cos(n)*Math.pow(Math.tan(Math.PI/4+n/2),h)/h,c=l*Math.pow(1/Math.tan(Math.PI/4+o/2),h),u=l*Math.pow(1/Math.tan(Math.PI/4+0),h);return{x:c*Math.sin(h*(a-s))*this.radius,y:-(u-c*Math.cos(h*(a-s)))*this.radius}}};Y.degRad=180/Math.PI,Y.radDeg=Math.PI/180,Y.radius=6381372;var E={_setupContainerEvents:function(){var t,e,i=this,s=!1,a=this;this.params.draggable&&(g.on(this.container,"mousemove",(function(i){if(!s)return!1;a.transX-=(t-i.pageX)/a.scale,a.transY-=(e-i.pageY)/a.scale,a._applyTransform(),t=i.pageX,e=i.pageY})),g.on(this.container,"mousedown",(function(i){return s=!0,t=i.pageX,e=i.pageY,!1})),g.on(document.body,"mouseup",(function(){s=!1}))),this.params.zoomOnScroll&&g.on(this.container,"wheel",(function(t){var e=0;e=(t.deltaY||-t.wheelDelta||t.detail)>>10||1,e*=75;var s=i.container.getBoundingClientRect(),n=t.pageX-s.left-window.pageXOffset,r=t.pageY-s.top-window.pageYOffset,o=Math.pow(1+a.params.zoomOnScrollSpeed/1e3,-1.5*e);a.tooltip&&a.tooltip.hide(),a._setScale(a.scale*o,n,r),t.preventDefault()}))},_setupElementEvents:function(){var t,e,i,s=this,a=this.container;g.on(a,"mousemove",(function(s){Math.abs(t-s.pageX)+Math.abs(e-s.pageY)>2&&(i=!0)})),g.delegate(a,"mousedown",".jvm-element",(function(s){t=s.pageX,e=s.pageY,i=!1})),g.delegate(a,"mouseover mouseout",".jvm-element",(function(t){var e=y(s,this,!0),i=s.params.showTooltip;"mouseover"===t.type?(e.element.hover(!0),s.tooltip.text(e.tooltipText),s._emit(e.event,[t,s.tooltip,e.code]),t.defaultPrevented||i&&s.tooltip.show()):(e.element.hover(!1),i&&s.tooltip.hide())})),g.delegate(a,"mouseup",".jvm-element",(function(t){var e=y(s,this);if(!i&&("region"===e.type&&s.params.regionsSelectable||"marker"===e.type&&s.params.markersSelectable)){var a=e.element;s.params[e.type+"sSelectableOne"]&&s._clearSelected(e.type+"s"),e.element.isSelected?a.select(!1):a.select(!0),s._emit(e.event,[e.code,a.isSelected,s._getSelected(e.type+"s")])}})),g.delegate(a,"click",".jvm-element",(function(t){var e=y(s,this),i=e.type,a=e.code;s._emit("region"===i?v.onRegionClick:v.onMarkerClick,[t,a])}))},_setupZoomButtons:function(){var t=this,e=this,i=l("div","jvm-zoom-btn jvm-zoomin","&#43;",!0),s=l("div","jvm-zoom-btn jvm-zoomout","&#x2212",!0);this.container.appendChild(i),this.container.appendChild(s),g.on(i,"click",(function(){t._setScale(e.scale*e.params.zoomStep,e.width/2,e.height/2,!1,e.params.zoomAnimate)})),g.on(s,"click",(function(){t._setScale(e.scale/e.params.zoomStep,e.width/2,e.height/2,!1,e.params.zoomAnimate)}))},_setupContainerTouchEvents:function(){var t,e,i,s,a,n,r,o=this,h=function(h){var l,c,u,p,d=h.touches;if("touchstart"==h.type&&(r=0),1==d.length)1==r&&(u=o.transX,p=o.transY,o.transX-=(i-d[0].pageX)/o.scale,o.transY-=(s-d[0].pageY)/o.scale,o.tooltip.hide(),o._applyTransform(),u==o.transX&&p==o.transY||h.preventDefault()),i=d[0].pageX,s=d[0].pageY;else if(2==d.length)if(2==r)c=Math.sqrt(Math.pow(d[0].pageX-d[1].pageX,2)+Math.pow(d[0].pageY-d[1].pageY,2))/e,o._setScale(t*c,a,n),o.tooltip.hide(),h.preventDefault();else{var f=o.container.selector.getBoundingClientRect();l={top:f.top+window.scrollY,left:f.left+window.scrollX},a=d[0].pageX>d[1].pageX?d[1].pageX+(d[0].pageX-d[1].pageX)/2:d[0].pageX+(d[1].pageX-d[0].pageX)/2,n=d[0].pageY>d[1].pageY?d[1].pageY+(d[0].pageY-d[1].pageY)/2:d[0].pageY+(d[1].pageY-d[0].pageY)/2,a-=l.left,n-=l.top,t=o.scale,e=Math.sqrt(Math.pow(d[0].pageX-d[1].pageX,2)+Math.pow(d[0].pageY-d[1].pageY,2))}r=d.length};g.on(o.container,"touchstart",h),g.on(o.container,"touchmove",h)},_createRegions:function(){var t,e;for(t in this._regionLabelsGroup=this._regionLabelsGroup||this.canvas.createGroup("jvm-regions-labels-group"),this.mapData.paths)e=new j({map:this,code:t,path:this.mapData.paths[t].path,style:u({},this.params.regionStyle),labelStyle:this.params.regionLabelStyle,labelsGroup:this._regionLabelsGroup,label:this.params.labels&&this.params.labels.regions}),this.regions[t]={config:this.mapData.paths[t],element:e}},_createLines:function(t,e,i){void 0===i&&(i=!1);var s=!1,a=!1;for(var n in this.linesGroup=this.linesGroup||this.canvas.createGroup("jvm-lines-group"),t){var r=t[n];for(var o in e){var h=i?e[o].config:e[o];h.name===r.from&&(s=this.getMarkerPosition(h)),h.name===r.to&&(a=this.getMarkerPosition(h))}!1!==s&&!1!==a&&(this.lines[p(r.from,r.to)]=new C({index:n,map:this,style:u({initial:this.params.lineStyle},{initial:r.style||{}},!0),x1:s.x,y1:s.y,x2:a.x,y2:a.y,group:this.linesGroup,config:r}))}},_createMarkers:function(t,e){var i,s,a,n,r=this;for(var o in void 0===t&&(t={}),void 0===e&&(e=!1),this._markersGroup=this._markersGroup||this.canvas.createGroup("jvm-markers-group"),this._markerLabelsGroup=this._markerLabelsGroup||this.canvas.createGroup("jvm-markers-labels-group"),t)if(i=t[o],a=this.getMarkerPosition(i),n=i.coords.join(":"),a){if(e){if(Object.keys(this.markers).filter((function(t){return r.markers[t]._uid===n})).length)continue;o=Object.keys(this.markers).length}s=new L({index:o,map:this,style:u(this.params.markerStyle,{initial:i.style||{}},!0),label:this.params.labels&&this.params.labels.markers,labelsGroup:this._markerLabelsGroup,cx:a.x,cy:a.y,group:this._markersGroup,marker:i,isRecentlyCreated:e}),this.markers[o]&&this.removeMarkers([o]),this.markers[o]={_uid:n,config:i,element:s}}},_createSeries:function(){for(var t in this.series={markers:[],regions:[]},this.params.series)for(var e=0;e<this.params.series[t].length;e++)this.series[t][e]=new X(this.params.series[t][e],this[t],this)},_applyTransform:function(){var t,e,i,s;this.defaultWidth*this.scale<=this.width?(t=(this.width-this.defaultWidth*this.scale)/(2*this.scale),i=(this.width-this.defaultWidth*this.scale)/(2*this.scale)):(t=0,i=(this.width-this.defaultWidth*this.scale)/this.scale),this.defaultHeight*this.scale<=this.height?(e=(this.height-this.defaultHeight*this.scale)/(2*this.scale),s=(this.height-this.defaultHeight*this.scale)/(2*this.scale)):(e=0,s=(this.height-this.defaultHeight*this.scale)/this.scale),this.transY>e?this.transY=e:this.transY<s&&(this.transY=s),this.transX>t?this.transX=t:this.transX<i&&(this.transX=i),this.canvas.applyTransformParams(this.scale,this.transX,this.transY),this.markers&&this._repositionMarkers(),this.lines&&this._repositionLines(),this._repositionLabels()},_resize:function(){var t=this.baseScale;this.width/this.height>this.defaultWidth/this.defaultHeight?(this.baseScale=this.height/this.defaultHeight,this.baseTransX=Math.abs(this.width-this.defaultWidth*this.baseScale)/(2*this.baseScale)):(this.baseScale=this.width/this.defaultWidth,this.baseTransY=Math.abs(this.height-this.defaultHeight*this.baseScale)/(2*this.baseScale)),this.scale*=this.baseScale/t,this.transX*=this.baseScale/t,this.transY*=this.baseScale/t},_setScale:function(t,e,i,s,a){var n,r,o,h,l,c,u,p,d,f,m=this,g=0,y=Math.abs(Math.round(60*(t-this.scale)/Math.max(t,this.scale)));t>this.params.zoomMax*this.baseScale?t=this.params.zoomMax*this.baseScale:t<this.params.zoomMin*this.baseScale&&(t=this.params.zoomMin*this.baseScale),void 0!==e&&void 0!==i&&(n=t/this.scale,s?(d=e+this.defaultWidth*(this.width/(this.defaultWidth*t))/2,f=i+this.defaultHeight*(this.height/(this.defaultHeight*t))/2):(d=this.transX-(n-1)/t*e,f=this.transY-(n-1)/t*i)),a&&y>0?(o=this.scale,h=(t-o)/y,l=this.transX*this.scale,u=this.transY*this.scale,c=(d*t-l)/y,p=(f*t-u)/y,r=setInterval((function(){g+=1,m.scale=o+h*g,m.transX=(l+c*g)/m.scale,m.transY=(u+p*g)/m.scale,m._applyTransform(),g==y&&(clearInterval(r),m._emit(v.onViewportChange,[m.scale,m.transX,m.transY]))}),10)):(this.transX=d,this.transY=f,this.scale=t,this._applyTransform(),this._emit(v.onViewportChange,[this.scale,this.transX,this.transY]))},setFocus:function(t){var e=this;void 0===t&&(t={});var i,s=[];if(t.region?s.push(t.region):t.regions&&(s=t.regions),s.length)return s.forEach((function(t){if(e.regions[t]){var s=e.regions[t].element.shape.getBBox();s&&(i=void 0===i?s:{x:Math.min(i.x,s.x),y:Math.min(i.y,s.y),width:Math.max(i.x+i.width,s.x+s.width)-Math.min(i.x,s.x),height:Math.max(i.y+i.height,s.y+s.height)-Math.min(i.y,s.y)})}})),this._setScale(Math.min(this.width/i.width,this.height/i.height),-(i.x+i.width/2),-(i.y+i.height/2),!0,t.animate);if(t.coords){var a=this.coordsToPoint(t.coords[0],t.coords[1]),n=this.transX-a.x/this.scale,r=this.transY-a.y/this.scale;return this._setScale(t.scale*this.baseScale,n,r,!0,t.animate)}},updateSize:function(){this.width=this.container.offsetWidth,this.height=this.container.offsetHeight,this._resize(),this.canvas.setSize(this.width,this.height),this._applyTransform()},coordsToPoint:function(t,e){var i,s,a,n=V.maps[this.params.map].projection,r=n.centralMeridian;return i=Y[n.type](t,e,r),!!(s=this.getInsetForPoint(i.x,i.y))&&(a=s.bbox,i.x=(i.x-a[0].x)/(a[1].x-a[0].x)*s.width*this.scale,i.y=(i.y-a[0].y)/(a[1].y-a[0].y)*s.height*this.scale,{x:i.x+this.transX*this.scale+s.left*this.scale,y:i.y+this.transY*this.scale+s.top*this.scale})},getInsetForPoint:function(t,e){var i,s,a=V.maps[this.params.map].insets;for(i=0;i<a.length;i++)if(t>(s=a[i].bbox)[0].x&&t<s[1].x&&e>s[0].y&&e<s[1].y)return a[i]},getMarkerPosition:function(t){var e=t.coords;return V.maps[this.params.map].projection?this.coordsToPoint.apply(this,e):{x:e[0]*this.scale+this.transX*this.scale,y:e[1]*this.scale+this.transY*this.scale}},_repositionLines:function(){var t=!1,e=!1;for(var i in this.lines){for(var s in this.markers){var a=this.markers[s];a.config.name===this.lines[i].config.from&&(t=this.getMarkerPosition(a.config)),a.config.name===this.lines[i].config.to&&(e=this.getMarkerPosition(a.config))}!1!==t&&!1!==e&&this.lines[i].setStyle({x1:t.x,y1:t.y,x2:e.x,y2:e.y})}},_repositionMarkers:function(){var t;for(var e in this.markers)!1!==(t=this.getMarkerPosition(this.markers[e].config))&&this.markers[e].element.setStyle({cx:t.x,cy:t.y})},_repositionLabels:function(){var t=this.params.labels;if(t){if(t.regions)for(var e in this.regions)this.regions[e].element.updateLabelPosition();if(t.markers)for(var i in this.markers)this.markers[i].element.updateLabelPosition()}}},T=function(){function t(t,e){this.node=this._createElement(t),e&&this.set(e)}var e=t.prototype;return e._createElement=function(t){return document.createElementNS("http://www.w3.org/2000/svg",t)},e.addClass=function(t){this.node.setAttribute("class",t)},e.getBBox=function(){return this.node.getBBox()},e.set=function(t,e){if("object"==typeof t)for(var i in t)this.applyAttr(i,t[i]);else this.applyAttr(t,e)},e.get=function(t){return this.style.initial[t]},e.applyAttr=function(t,e){this.node.setAttribute(t.replace(/[\w]([A-Z])/g,(function(t){return t[0]+"-"+t[1]})).toLowerCase(),e)},e.remove=function(){c(this.node)},t}(),z=function(t){function e(e,i,s){var a;return void 0===s&&(s={}),(a=t.call(this,e,i)||this).isHovered=!1,a.isSelected=!1,a.style=s,a.style.current={},a.updateStyle(),a}b(e,t);var i=e.prototype;return i.setStyle=function(t,e){var i;"object"==typeof t?u(this.style.current,t):u(this.style.current,((i={})[t]=e,i));this.updateStyle()},i.updateStyle=function(){var t={};u(t,this.style.initial),u(t,this.style.current),this.isHovered&&u(t,this.style.hover),this.isSelected&&(u(t,this.style.selected),this.isHovered&&u(t,this.style.selectedHover)),this.set(t)},e}(T),P=function(t){function e(e,i){return t.call(this,"text",e,i)||this}return b(e,t),e.prototype.applyAttr=function(e,i){"text"===e?this.node.textContent=i:t.prototype.applyAttr.call(this,e,i)},e}(z),D=function(t){function e(e,i){return t.call(this,"image",e,i)||this}return b(e,t),e.prototype.applyAttr=function(e,i){var s;"image"===e?("object"==typeof i?(s=i.url,this.offset=i.offset||[0,0]):(s=i,this.offset=[0,0]),this.node.setAttributeNS("http://www.w3.org/1999/xlink","href",s),this.width=23,this.height=23,this.applyAttr("width",this.width),this.applyAttr("height",this.height),this.applyAttr("x",this.cx-this.width/2+this.offset[0]),this.applyAttr("y",this.cy-this.height/2+this.offset[1])):"cx"==e?(this.cx=i,this.width&&this.applyAttr("x",i-this.width/2+this.offset[0])):"cy"==e?(this.cy=i,this.height&&this.applyAttr("y",i-this.height/2+this.offset[1])):t.prototype.applyAttr.apply(this,arguments)},e}(z),I=function(t){function e(e){var i;return(i=t.call(this,"svg")||this)._container=e,i._defsElement=new T("defs"),i._rootElement=new T("g",{id:"jvm-regions-group"}),i.node.appendChild(i._defsElement.node),i.node.appendChild(i._rootElement.node),i._container.appendChild(i.node),i}b(e,t);var i=e.prototype;return i.setSize=function(t,e){this.node.setAttribute("width",t),this.node.setAttribute("height",e)},i.applyTransformParams=function(t,e,i){this._rootElement.node.setAttribute("transform","scale("+t+") translate("+e+", "+i+")")},i.createPath=function(t,e){var i=new z("path",t,e);return i.node.setAttribute("fill-rule","evenodd"),this.add(i)},i.createCircle=function(t,e,i){var s=new z("circle",t,e);return this.add(s,i)},i.createLine=function(t,e,i){var s=new z("line",t,e);return this.add(s,i)},i.createText=function(t,e,i){var s=new P(t,e);return this.add(s,i)},i.createImage=function(t,e,i){var s=new D(t,e);return this.add(s,i)},i.createGroup=function(t){var e=new T("g");return this.node.appendChild(e.node),t&&(e.node.id=t),e.canvas=this,e},i.add=function(t,e){return(e=e||this._rootElement).node.appendChild(t.node),t},e}(T),R=function(t){function e(e){var i;i=t.call(this)||this;var s=l("div","jvm-tooltip");return i._map=e,i._tooltip=document.body.appendChild(s),i._bindEventListeners(),w(i)||w(i)}b(e,t);var i=e.prototype;return i._bindEventListeners=function(){var t=this;g.on(this._map.container,"mousemove",(function(e){if(t._tooltip.classList.contains("active")){var i,s,a=(i=t._map.container,s="#jvm-regions-group",Element.prototype.querySelector.call(i,s)).getBoundingClientRect(),n=t._tooltip.getBoundingClientRect(),r=n.height,o=n.width,h=e.clientY<=a.top+r+5,l=e.pageY-r-5,c=e.pageX-o-5;h&&(l+=r+5,c-=10),e.clientX<a.left+o+5&&(c=e.pageX+5+2,h&&(c+=10)),t.css({top:l+"px",left:c+"px"})}}))},i.getElement=function(){return this._tooltip},i.show=function(){this._tooltip.classList.add("active")},i.hide=function(){this._tooltip.classList.remove("active")},i.text=function(t,e){void 0===e&&(e=!1);var i=e?"innerHTML":"textContent";if(!t)return this._tooltip[i];this._tooltip[i]=t},i.css=function(t){for(var e in t)this._tooltip.style[e]=t[e];return this},e}(x),H=function(){function t(t,e){var i=t.scale,s=t.values;this._scale=i,this._values=s,this._fromColor=this.hexToRgb(i[0]),this._toColor=this.hexToRgb(i[1]),this._map=e,this.setMinMaxValues(s),this.visualize()}var e=t.prototype;return e.setMinMaxValues=function(t){for(var e in this.min=Number.MAX_VALUE,this.max=0,t)(e=parseFloat(t[e]))>this.max&&(this.max=e),e<this.min&&(this.min=e)},e.visualize=function(){var t,e={};for(var i in this._values)t=parseFloat(this._values[i]),isNaN(t)||(e[i]=this.getValue(t));this.setAttributes(e)},e.setAttributes=function(t){for(var e in t)this._map.regions[e]&&this._map.regions[e].element.setStyle("fill",t[e])},e.getValue=function(t){for(var e,i="#",s=0;s<3;s++)i+=(1===(e=Math.round(this._fromColor[s]+(this._toColor[s]-this._fromColor[s])*((t-this.min)/(this.max-this.min))).toString(16)).length?"0":"")+e;return i},e.hexToRgb=function(t){var e=0,i=0,s=0;return 4==t.length?(e="0x"+t[1]+t[1],i="0x"+t[2]+t[2],s="0x"+t[3]+t[3]):7==t.length&&(e="0x"+t[1]+t[2],i="0x"+t[3]+t[4],s="0x"+t[5]+t[6]),[parseInt(e),parseInt(i),parseInt(s)]},t}(),V=function(){function t(e){var i=this;if(void 0===e&&(e={}),this.params=u(t.defaults,e,!0),!t.maps[this.params.map])throw new Error("Attempt to use map which was not loaded: "+e.map);this.mapData=t.maps[this.params.map],this.regions={},this.markers={},this.lines={},this.defaultWidth=this.mapData.width,this.defaultHeight=this.mapData.height,this.height=0,this.width=0,this.scale=1,this.baseScale=1,this.transX=0,this.transY=0,this.baseTransX=0,this.baseTransY=0,"loading"!==document.readyState?this._init():window.addEventListener("DOMContentLoaded",(function(){return i._init()}))}var e=t.prototype;return e._init=function(){var t=this.params;this.container=h(t.selector),this.container.classList.add("jvm-container"),this.canvas=new I(this.container),this.setBackgroundColor(t.backgroundColor),this._createRegions(),this.updateSize(),this._createLines(t.lines||{},t.markers||{}),this._createMarkers(t.markers),this._repositionLabels(),this._setupContainerEvents(),this._setupElementEvents(),t.zoomButtons&&this._setupZoomButtons(),t.showTooltip&&(this.tooltip=new R(this)),t.selectedRegions&&this._setSelected("regions",t.selectedRegions),t.selectedMarkers&&this._setSelected("markers",t.selectedMarkers),t.focusOn&&this.setFocus(t.focusOn),t.visualizeData&&(this.dataVisualization=new H(t.visualizeData,this)),t.bindTouchEvents&&("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)&&this._setupContainerTouchEvents(),t.series&&(this.container.appendChild(this.legendHorizontal=l("div","jvm-series-container jvm-series-h")),this.container.appendChild(this.legendVertical=l("div","jvm-series-container jvm-series-v")),this._createSeries()),this._emit(v.onLoaded,[this])},e._emit=function(t,e){for(var i in v)v[i]===t&&"function"==typeof this.params[i]&&this.params[i].apply(this,e)},e.setBackgroundColor=function(t){this.container.style.backgroundColor=t},e._getSelected=function(t){var e,i=[];for(e in this[t])this[t][e].element.isSelected&&i.push(e);return i},e._setSelected=function(t,e){var i=this;e.forEach((function(e){i[t][e]&&i[t][e].element.select(!0)}))},e._clearSelected=function(t){var e=this;this._getSelected(t).forEach((function(i){e[t][i].element.select(!1)}))},e.getSelectedRegions=function(){return this._getSelected("regions")},e.clearSelectedRegions=function(){this._clearSelected("regions")},e.getSelectedMarkers=function(){return this._getSelected("markers")},e.clearSelectedMarkers=function(){this._clearSelected("markers")},e.addMarkers=function(t){if(Array.isArray(t))return this._createMarkers(t,!0);this._createMarkers([t],!0)},e.removeMarkers=function(t){var e=this;t||(t=Object.keys(this.markers)),t.forEach((function(t){e.markers[t].element.remove(),delete e.markers[t]}))},e.addLine=function(t,e,i){void 0===i&&(i={}),console.warn("`addLine` method is deprecated, please use `addLines` instead."),this._createLines([{from:t,to:e,style:i}],this.markers,!0)},e.addLines=function(t){var e=this._getLinesAsUids();Array.isArray(t)||(t=[t]),this._createLines(t.filter((function(t){return!(e.indexOf(p(t.from,t.to))>-1)})),this.markers,!0)},e.removeLines=function(t){var e=this;(t=Array.isArray(t)?t.map((function(t){return p(t.from,t.to)})):this._getLinesAsUids()).forEach((function(t){e.lines[t].dispose(),delete e.lines[t]}))},e._getLinesAsUids=function(){return Object.keys(this.lines)},e.removeLine=function(t,e){console.warn("`removeLine` method is deprecated, please use `removeLines` instead.");var i=p(t,e);this.lines.hasOwnProperty(i)&&(this.lines[i].element.remove(),delete this.lines[i])},e.reset=function(){for(var t in this.series)for(var e=0;e<this.series[t].length;e++)this.series[t][e].clear();this.legendHorizontal&&(c(this.legendHorizontal),this.legendHorizontal=null),this.legendVertical&&(c(this.legendVertical),this.legendVertical=null),this.scale=this.baseScale,this.transX=this.baseTransX,this.transY=this.baseTransY,this._applyTransform(),this.clearSelectedMarkers(),this.clearSelectedRegions(),this.removeMarkers()},e.destroy=function(t){var e=this;void 0===t&&(t=!0),g.flush(),this.tooltip.dispose(),this._emit(v.onDestroyed),t&&Object.keys(this).forEach((function(t){try{delete e[t]}catch(t){}}))},e.extend=function(e,i){if("function"==typeof this[e])throw new Error("The method ["+e+"] already exists internally please use another name.");t.prototype[e]=i},t}();V.maps={},V.defaults={map:"world",backgroundColor:"transparent",draggable:!0,zoomButtons:!0,zoomOnScroll:!0,zoomOnScrollSpeed:3,zoomMax:12,zoomMin:1,zoomAnimate:!0,showTooltip:!0,zoomStep:1.5,bindTouchEvents:!0,lineStyle:{stroke:"#808080",strokeWidth:1,strokeLinecap:"round"},markersSelectable:!1,markersSelectableOne:!1,markerStyle:{initial:{r:7,fill:"#374151",fillOpacity:1,stroke:"#FFF",strokeWidth:5,strokeOpacity:.5},hover:{fill:"#3cc0ff",cursor:"pointer"},selected:{fill:"blue"},selectedHover:{}},markerLabelStyle:{initial:{fontFamily:"Verdana",fontSize:12,fontWeight:500,cursor:"default",fill:"#374151"},hover:{cursor:"pointer"},selected:{},selectedHover:{}},regionsSelectable:!1,regionsSelectableOne:!1,regionStyle:{initial:{fill:"#dee2e8",fillOpacity:1,stroke:"none",strokeWidth:0},hover:{fillOpacity:.7,cursor:"pointer"},selected:{fill:"#9ca3af"},selectedHover:{}},regionLabelStyle:{initial:{fontFamily:"Verdana",fontSize:"12",fontWeight:"bold",cursor:"default",fill:"#35373e"},hover:{cursor:"pointer"}}},Object.assign(V.prototype,E);var G=function(){function t(t){if(void 0===t&&(t={}),!t.selector)throw new Error("Selector is not given.");return new V(t)}return t.addMap=function(t,e){V.maps[t]=e},t}();return window.jsVectorMap=G}));
